2025-06-26 17:18:25,676 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:18:26,498 - root - INFO - Loaded 39 trade records from logs/trades\trade_log_********.json
2025-06-26 17:18:26,499 - root - INFO - Loaded 24 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 17:18:26,500 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:18:27,483 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:18:27,489 - root - INFO - Configuration loaded successfully.
2025-06-26 17:18:27,497 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:18:27,503 - root - INFO - Configuration loaded successfully.
2025-06-26 17:18:27,503 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:18:27,509 - root - INFO - Configuration loaded successfully.
2025-06-26 17:18:27,511 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:18:27,511 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:18:28,321 - root - INFO - Telegram command handlers registered
2025-06-26 17:18:28,321 - root - INFO - Telegram bot polling started
2025-06-26 17:18:28,322 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:18:28,322 - root - INFO - Telegram notification channel initialized
2025-06-26 17:18:28,323 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:18:28,324 - root - INFO - Loaded 24 templates from file
2025-06-26 17:18:28,324 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:18:28,324 - root - INFO - Notification manager initialized
2025-06-26 17:18:28,325 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:18:28,325 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:18:28,328 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:18:28,328 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:18:28,331 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:18:28,333 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 17:18:28,333 - root - INFO - Recovery manager initialized
2025-06-26 17:18:28,337 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:18:28,345 - root - INFO - Configuration loaded successfully.
2025-06-26 17:18:28,347 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:18:28,347 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:18:28,347 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:18:28,348 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:18:28,348 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:18:28,348 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:18:28,348 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:18:28,348 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:18:28,348 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:18:28,354 - root - INFO - Configuration loaded successfully.
2025-06-26 17:18:28,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:18:28,398 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:18:28,398 - telegram.ext.Application - INFO - Application started
2025-06-26 17:18:28,648 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:18:28,648 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:18:28,648 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:18:28,649 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:18:28,649 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:18:28,649 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:18:28,657 - root - INFO - Configuration loaded successfully.
2025-06-26 17:18:28,661 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:18:28,667 - root - INFO - Configuration loaded successfully.
2025-06-26 17:18:28,668 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:18:28,675 - root - INFO - Configuration loaded successfully.
2025-06-26 17:18:28,675 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 17:18:28,676 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 17:18:28,676 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 17:18:28,676 - root - INFO - Trading mode: paper
2025-06-26 17:18:28,676 - root - INFO - Trading enabled: True
2025-06-26 17:18:28,677 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:18:28,677 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:18:28,677 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:18:28,677 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:18:28,677 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:18:28,684 - root - INFO - Configuration loaded successfully.
2025-06-26 17:18:28,921 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:18:28,922 - root - INFO - Trading enabled in paper mode
2025-06-26 17:18:28,922 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:18:28,922 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 17:18:28,922 - root - INFO - Reset paper trading account to initial balance
2025-06-26 17:18:28,923 - root - INFO - Generated run ID: ********_171828
2025-06-26 17:18:28,923 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 17:18:28,923 - root - INFO - Background service initialized
2025-06-26 17:18:28,924 - root - INFO - Network watchdog started
2025-06-26 17:18:28,924 - root - INFO - Network watchdog started
2025-06-26 17:18:28,926 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 17:18:28,926 - root - INFO - Background service started
2025-06-26 17:18:28,927 - root - INFO - Executing strategy (run #1)...
2025-06-26 17:18:28,927 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 17:18:28,927 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 17:18:28,928 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 17:18:28,929 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 17:18:28,995 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
