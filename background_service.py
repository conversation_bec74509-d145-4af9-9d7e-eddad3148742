#!/usr/bin/env python
"""
Background service for running the asset rotation strategy continuously.
"""

import os
import sys
import logging
import time
import threading
import schedule
from datetime import datetime, timedelta
import argparse
import signal
import json
import pandas as pd
import matplotlib.pyplot as plt

# Configure logging to capture all logs including from external libraries
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"background_service_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

# Set httpx logging level to INFO to capture Telegram API connection logs
logging.getLogger('httpx').setLevel(logging.INFO)
logging.getLogger('telegram').setLevel(logging.INFO)

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import necessary modules
from src.config_manager import load_config, get_trading_config, load_notification_config
from src.trading.executor import TradingExecutor
from src.trading.account import AccountManager
from src.trading.trade_logger import trade_logger
from main_program import run_strategy_for_web
# Removed import of save_backtest_chart as it's not needed
from src.incremental_fetcher import fetch_real_time_data
from src.state_manager import load_state, save_state, update_data_timestamps_from_dataframes
from src.data_cache import cleanup_cache_file
from src.performance_tracker import ensure_metrics_directory, load_metrics_history, plot_metrics_history

# Import network watchdog and recovery manager
from src.network_watchdog import NetworkWatchdog
from src.recovery_manager import RecoveryManager

# Import notification modules
try:
    from src.notification.notification_manager import NotificationManager
    NOTIFICATIONS_AVAILABLE = True
except ImportError:
    NOTIFICATIONS_AVAILABLE = False
    logging.warning("Notification modules not available. Notifications will be disabled.")

class BackgroundService:
    """
    Background service for running the asset rotation strategy continuously.
    """

    def __init__(self, config_path=None, notification_config_path=None, test_mode=False):
        """Initialize the background service."""
        print("\n" + "=" * 50)
        print("INITIALIZING BACKGROUND SERVICE")
        if test_mode:
            print("*** RUNNING IN TEST MODE ***")
        print("=" * 50)

        # Store test mode flag and config path
        self.test_mode = test_mode
        self.config_path = config_path

        # Load configuration
        print("Loading configuration...")
        self.config = load_config(config_path)
        self.trading_config = get_trading_config(config_path)
        self.notification_config = load_notification_config(notification_config_path)

        # Log key configuration settings
        settings = self.config.get('settings', {})
        timeframe = settings.get('timeframe', '1d')

        # Get separate asset lists for trend detection vs trading
        # Use exchange-agnostic defaults
        from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
        exchange_id = self.config.get('exchange', 'binance')
        trading_quote = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')

        trend_assets = settings.get('trend_assets', settings.get('assets', ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']))
        trading_assets = settings.get('assets', [f'BTC/{trading_quote}', f'ETH/{trading_quote}', f'SOL/{trading_quote}'])

        print(f"Configured timeframe: {timeframe}")
        print(f"Trend detection assets (USDT): {', '.join(trend_assets[:3])}{' and more' if len(trend_assets) > 3 else ''}")
        print(f"Trading assets (USDC): {', '.join(trading_assets[:3])}{' and more' if len(trading_assets) > 3 else ''}")
        print(f"Using MTPI signal: {settings.get('use_mtpi_signal', True)}")
        print(f"Trend method: {settings.get('trend_method', 'PGO For Loop')}")
        print(f"Incremental data fetching: {not settings.get('force_refresh_cache', False)}")

        # Initialize components
        self.initialize_components()

        # State variables
        self.is_running = False
        self.scheduler_thread = None
        self.last_execution_time = None
        self.last_mtpi_signal = None
        self.last_mtpi_score = None
        self.last_best_asset = None
        self.execution_count = 0
        self.missed_executions = 0
        self.last_network_status = True  # Assume network is initially connected

        # Generate a unique run ID for this service instance
        # This will be used to create new metrics files for each run
        self.run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        print(f"Generated run ID: {self.run_id}")
        logging.info(f"Generated run ID: {self.run_id}")

        # Set up metrics directory
        self.metrics_dir = "Performance_Metrics"
        ensure_metrics_directory(self.metrics_dir)
        logging.info(f"Ensured metrics directory exists: {self.metrics_dir}")
        print(f"Metrics will be saved to: {self.metrics_dir}")

        # Register signal handlers
        signal.signal(signal.SIGINT, self.handle_shutdown)
        signal.signal(signal.SIGTERM, self.handle_shutdown)

        logging.info("Background service initialized")
        print("Background service initialized successfully")

    def initialize_components(self):
        """Initialize service components."""
        try:
            # Initialize notification manager
            if NOTIFICATIONS_AVAILABLE and self.notification_config.get('telegram', {}).get('enabled', False):
                # Modify notification config for test mode
                notification_config = self.notification_config.copy()
                if self.test_mode:
                    # Use test bot credentials
                    telegram_config = notification_config.get('telegram', {}).copy()
                    test_token = os.getenv('TELEGRAM_BOT_TOKEN_TEST')
                    test_chat_id = os.getenv('TELEGRAM_CHAT_ID_TEST')

                    if test_token and test_chat_id:
                        telegram_config['token'] = test_token
                        telegram_config['chat_id'] = test_chat_id
                        notification_config['telegram'] = telegram_config
                        logging.info("Using test Telegram bot credentials")
                        print("Using test Telegram bot credentials")
                    else:
                        logging.warning("Test mode enabled but test Telegram credentials not found in environment")
                        print("WARNING: Test mode enabled but test Telegram credentials not found in environment")
                        print("Please set TELEGRAM_BOT_TOKEN_TEST and TELEGRAM_CHAT_ID_TEST environment variables")

                self.notification_manager = NotificationManager(notification_config)
                logging.info("Notification manager initialized")
            else:
                self.notification_manager = None
                logging.warning("Telegram notifications disabled or not available")

            # Initialize network watchdog
            self.initialize_network_watchdog()

            # Initialize recovery manager
            self.initialize_recovery_manager()

            # Initialize trading components
            exchange_id = self.trading_config.get('exchange', 'binance')
            logging.error(f"🚨 TRADING EXECUTOR DEBUG - Initializing with exchange: {exchange_id}")
            logging.error(f"🚨 TRADING EXECUTOR DEBUG - Trading config: {self.trading_config}")
            self.trading_executor = TradingExecutor(exchange_id, test_mode=self.test_mode, config_path=self.config_path)
            self.account_manager = AccountManager(exchange_id, test_mode=self.test_mode, config_path=self.config_path)

            # Test trading connection
            if self.trading_config.get('enabled', False):
                mode = self.trading_config.get('mode', 'paper')
                logging.info(f"Trading enabled in {mode} mode")

                # Reset paper trading account if in paper mode
                if mode == 'paper' and hasattr(self.trading_executor, 'paper_trading'):
                    self.trading_executor.paper_trading.reset()
                    logging.info("Reset paper trading account to initial balance")

                # Test connection
                if mode == 'live':
                    # Detect the correct quote currency for this exchange
                    from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
                    quote_currency = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')
                    balance = self.account_manager.get_balance(quote_currency)
                    logging.info(f"Connected to {exchange_id}, balance: {balance} {quote_currency}")
            else:
                logging.warning("Trading is disabled in configuration")

            # We'll send a notification when the service starts instead of on initialization
            pass

        except Exception as e:
            logging.error(f"Error initializing components: {e}")
            raise

    def initialize_network_watchdog(self):
        """Initialize the network watchdog."""
        print("\nInitializing network watchdog...")

        # Define recovery callback
        def network_recovery_callback(downtime_seconds):
            """Callback function when network connectivity is restored."""
            logging.info(f"Network recovery callback triggered after {downtime_seconds:.1f} seconds downtime")

            # Make the recovery message EXTREMELY visible
            print("\n\n")
            print("!" * 100)
            print("!" + " " * 98 + "!")
            print("!" + " " * 30 + "NETWORK CONNECTIVITY RESTORED" + " " * 30 + "!")
            print("!" + " " * 98 + "!")
            print(f"! Connection was down for {downtime_seconds:.1f} seconds" + " " * (70 - len(f"{downtime_seconds:.1f}")) + "!")
            print(f"! Restored at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}" + " " * (70 - len(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")) + "!")
            print("!" + " " * 98 + "!")
            print("!" * 100)
            print("\n\n")

            # Test connectivity to key services
            print("Testing connectivity to key services...")
            try:
                import requests
                services = [
                    "https://www.google.com",
                    "https://api.binance.com/api/v3/ping",
                    "https://api.telegram.org"
                ]

                for service in services:
                    try:
                        start_time = time.time()
                        response = requests.get(service, timeout=5)
                        elapsed = time.time() - start_time
                        if response.status_code < 400:
                            print(f"✓ {service} is reachable (response time: {elapsed:.2f}s)")
                        else:
                            print(f"✗ {service} returned status code {response.status_code}")
                    except Exception as e:
                        print(f"✗ {service} is not reachable: {str(e)}")
            except Exception as e:
                print(f"Error testing connectivity: {e}")

            print("\nChecking for missed executions...")
            # Check if we missed any scheduled executions
            self.check_missed_executions()

            # Notify about network recovery
            if self.notification_manager:
                print("Sending network recovery notification...")
                # Format downtime string
                downtime_str = f"Downtime: {downtime_seconds:.1f} seconds\n" if downtime_seconds > 0 else ""
                # Format missed executions string
                missed_executions_str = f"Missed executions: {self.missed_executions}\n" if self.missed_executions > 0 else ""

                # Log the notification attempt
                logging.info("Attempting to send network recovery notification via Telegram")

                try:
                    # Get timeframe from config
                    timeframe = self.config.get('settings', {}).get('timeframe', '1d')

                    success = self.notification_manager.notify(
                        'network_status',
                        {
                            'status': '✅ RESTORED',
                            'downtime_str': downtime_str,
                            'in_critical_window': 'No' if not self.network_watchdog._is_in_critical_window(datetime.now(), timeframe) else 'Yes',
                            'missed_executions_str': missed_executions_str
                        }
                    )

                    if success:
                        print("✓ Network recovery notification sent successfully!")
                        logging.info("Network recovery notification sent successfully")
                    else:
                        print("✗ Failed to send network recovery notification!")
                        logging.warning("Failed to send network recovery notification")
                except Exception as e:
                    print(f"✗ Error sending network recovery notification: {e}")
                    logging.error(f"Error sending network recovery notification: {e}")

            print("\nNetwork recovery process completed!")
            return True

        # Define state save callback
        def state_save_callback():
            """Callback function to save state when network issues are detected."""
            logging.info("Saving state due to network issues")

            # Make the network disconnection message EXTREMELY visible
            print("\n\n")
            print("!" * 100)
            print("!" + " " * 98 + "!")
            print("!" + " " * 30 + "NETWORK CONNECTIVITY LOST" + " " * 32 + "!")
            print("!" + " " * 98 + "!")
            print(f"! Disconnected at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}" + " " * (70 - len(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")) + "!")
            print("!" + " " * 98 + "!")
            print("!" * 100)
            print("\n")
            print("SAVING CURRENT STATE DUE TO NETWORK ISSUES...")

            # Create state dictionary
            state = {
                'last_execution_time': self.last_execution_time.isoformat() if self.last_execution_time else None,
                'last_mtpi_signal': self.last_mtpi_signal,
                'last_best_asset': self.last_best_asset,
                'execution_count': self.execution_count,
                'missed_executions': self.missed_executions,
                'run_id': self.run_id,
                'timestamp': datetime.now().isoformat()
            }

            # Save state to file
            result = save_state(state, f"background_service_{self.run_id}")

            if result:
                print(f"✓ STATE SAVED SUCCESSFULLY")

                # Notify about network disconnection
                if self.notification_manager:
                    print("Sending network disconnection notification...")
                    # Format last execution string
                    last_exec_str = f"Last execution: {self.last_execution_time.isoformat()}\n" if self.last_execution_time else "Last execution: Never\n"

                    try:
                        # Get timeframe from config
                        timeframe = self.config.get('settings', {}).get('timeframe', '1d')

                        self.notification_manager.notify(
                            'network_status',
                            {
                                'status': '❌ DISCONNECTED',
                                'downtime_str': last_exec_str,
                                'in_critical_window': 'Yes' if self.network_watchdog._is_in_critical_window(datetime.now(), timeframe) else 'No',
                                'missed_executions_str': ''
                            }
                        )
                        print("✓ Network disconnection notification sent")
                    except Exception as e:
                        print(f"✗ Failed to send network disconnection notification: {e}")
                        logging.error(f"Failed to send network disconnection notification: {e}")
            else:
                print(f"✗ FAILED TO SAVE STATE")

            return result

        # Create the network watchdog
        timeframe = self.config.get('settings', {}).get('timeframe', '1d')
        check_interval = 10  # Default to 10 seconds (changed from 60 for faster detection)

        # Adjust check interval based on timeframe
        if timeframe == '1m':
            check_interval = 5  # Check every 5 seconds for 1m timeframe

        self.network_watchdog = NetworkWatchdog(
            check_interval=check_interval,
            recovery_callback=network_recovery_callback,
            state_save_callback=state_save_callback,
            max_failures=1  # Consider network down after 1 consecutive failure (changed from 3)
        )

        # Set up critical times based on timeframe
        self._setup_critical_times(timeframe)

        logging.info(f"Network watchdog initialized with {check_interval}s check interval")
        print(f"Network watchdog initialized with {check_interval}s check interval")

    def _setup_critical_times(self, timeframe):
        """Set up critical time windows based on timeframe."""
        # Clear existing critical times
        self.network_watchdog.critical_times = []

        # Set the current timeframe in the network watchdog
        self.network_watchdog.current_timeframe = timeframe

        # Add critical times for daily candle close - these are always important
        self.network_watchdog.add_critical_time(
            hour=23,
            minute=55,
            duration_minutes=10,
            description="Before daily candle close",
            timeframe="1d"
        )

        self.network_watchdog.add_critical_time(
            hour=0,
            minute=0,
            duration_minutes=10,
            description="After daily candle close",
            timeframe="1d"
        )

        # Add critical times for 4h candle closes
        if timeframe == '4h':
            for hour in [0, 4, 8, 12, 16, 20]:
                self.network_watchdog.add_critical_time(
                    hour=hour,
                    minute=0,
                    duration_minutes=5,
                    description=f"{hour}:00 4h candle close",
                    timeframe="4h"
                )

        # Add critical times for 1h candle closes
        if timeframe == '1h':
            for hour in range(24):
                self.network_watchdog.add_critical_time(
                    hour=hour,
                    minute=0,
                    duration_minutes=2,
                    description=f"{hour}:00 1h candle close",
                    timeframe="1h"
                )

        logging.info(f"Set up critical time windows for {timeframe} timeframe")

    def initialize_recovery_manager(self):
        """Initialize the recovery manager."""
        print("\nInitializing recovery manager...")

        # Define recovery callbacks
        recovery_callbacks = {
            'missed_execution': self.recover_missed_execution,
            'network_failure': self.recover_from_network_failure,
            'data_fetch_failure': self.recover_from_data_fetch_failure,
            'strategy_execution_failure': self.recover_from_strategy_failure
        }

        # Create the recovery manager
        self.recovery_manager = RecoveryManager(
            state_dir='data/state',
            checkpoint_interval=300,  # 5 minutes
            max_recovery_attempts=5,
            recovery_callbacks=recovery_callbacks
        )

        # Load previous state if available
        self.load_previous_state()

        logging.info("Recovery manager initialized")
        print("Recovery manager initialized")

    def load_previous_state(self):
        """Load previous state if available."""
        # Check for state files in the state directory
        state_dir = 'data/state'
        if not os.path.exists(state_dir):
            os.makedirs(state_dir, exist_ok=True)
            logging.info(f"Created state directory: {state_dir}")
            return

        # Look for state files matching our pattern
        state_files = [f for f in os.listdir(state_dir) if f.startswith('background_service_') and f.endswith('.json')]
        if not state_files:
            logging.info("No previous state files found")
            return

        # Sort by modification time (newest first)
        state_files.sort(key=lambda f: os.path.getmtime(os.path.join(state_dir, f)), reverse=True)

        # Try to load the newest state file
        latest_state_file = os.path.join(state_dir, state_files[0])
        try:
            state = load_state(latest_state_file)
            if state:
                logging.info(f"Loaded previous state from {latest_state_file}")
                print(f"Loaded previous state from {latest_state_file}")

                # Restore state variables
                if 'last_execution_time' in state and state['last_execution_time']:
                    self.last_execution_time = datetime.fromisoformat(state['last_execution_time'])
                if 'last_mtpi_signal' in state:
                    self.last_mtpi_signal = state['last_mtpi_signal']
                if 'last_best_asset' in state:
                    self.last_best_asset = state['last_best_asset']
                if 'execution_count' in state:
                    self.execution_count = state['execution_count']
                if 'missed_executions' in state:
                    self.missed_executions = state['missed_executions']

                return True
        except Exception as e:
            logging.error(f"Error loading previous state: {e}")
            print(f"Error loading previous state: {e}")

        return False

    def check_missed_executions(self):
        """Check if any scheduled executions were missed during network downtime."""
        if not self.last_execution_time:
            logging.info("No previous execution time, cannot check for missed executions")
            print("No previous execution time, cannot check for missed executions")
            return

        # Get the current time
        now = datetime.now()

        # Get timeframe from config
        timeframe = self.config.get('settings', {}).get('timeframe', '1d')

        # Calculate when the next execution should have been
        if timeframe == '1d':
            # For daily timeframe, next execution is at 00:01 UTC
            expected_next = self.last_execution_time.replace(hour=0, minute=1, second=0, microsecond=0)
            if expected_next <= self.last_execution_time:
                expected_next += timedelta(days=1)
        elif timeframe == '4h':
            # For 4h timeframe, next execution is at the next 4-hour mark
            hour = self.last_execution_time.hour
            next_hour = ((hour // 4) + 1) * 4 % 24
            expected_next = self.last_execution_time.replace(hour=next_hour, minute=1, second=0, microsecond=0)
            if expected_next <= self.last_execution_time:
                expected_next += timedelta(days=1)
        elif timeframe == '1h':
            # For 1h timeframe, next execution is at the next hour
            expected_next = self.last_execution_time.replace(minute=1, second=0, microsecond=0) + timedelta(hours=1)
        else:
            # Default to 1 hour later
            expected_next = self.last_execution_time + timedelta(hours=1)

        # Check if we missed the expected execution
        if expected_next < now:
            self.missed_executions += 1
            logging.warning(f"Missed execution detected! Expected at {expected_next}, current time is {now}")

            # Make the missed execution message very visible
            print("\n" + "!" * 100)
            print("!" + " " * 98 + "!")
            print("!" + " " * 30 + "MISSED EXECUTION DETECTED" + " " * 30 + "!")
            print("!" + " " * 98 + "!")
            print(f"! Expected execution at: {expected_next}" + " " * (70 - len(f"{expected_next}")) + "!")
            print(f"! Current time is: {now}" + " " * (70 - len(f"{now}")) + "!")
            print("!" + " " * 98 + "!")
            print("!" * 100)
            print("\nAttempting to recover from missed execution...")

            # Add a pending operation to recover from the missed execution
            self.recovery_manager.add_pending_operation(
                'missed_execution',
                {
                    'expected_time': expected_next.isoformat(),
                    'current_time': now.isoformat(),
                    'timeframe': timeframe
                }
            )

            # Process pending operations
            print("Processing pending recovery operations...")
            self.recovery_manager.process_pending_operations()

            # Send notification
            if self.notification_manager:
                print("Sending missed execution notification...")
                self.notification_manager.notify(
                    'missed_execution',
                    {
                        'expected_time': expected_next.isoformat(),
                        'current_time': now.isoformat(),
                        'timeframe': timeframe
                    }
                )
            print("Recovery process completed!")
        else:
            print(f"✓ No missed executions detected. Next execution expected at {expected_next}")

    def recover_missed_execution(self, details):
        """Recover from a missed execution."""
        logging.info(f"Recovering from missed execution: {details}")
        print(f"\nRECOVERING FROM MISSED EXECUTION: {details}")

        # Execute the strategy with recovery flag
        return self.execute_strategy(is_recovery=True)

    def recover_from_network_failure(self, details):
        """Recover from a network failure."""
        logging.info(f"Recovering from network failure: {details}")
        print(f"\nRECOVERING FROM NETWORK FAILURE: {details}")

        # Check if we're connected now
        if not self.network_watchdog.is_connected:
            logging.warning("Still not connected to network, cannot recover yet")
            print("STILL NOT CONNECTED TO NETWORK, CANNOT RECOVER YET")
            return False

        # Check for missed executions
        self.check_missed_executions()

        return True

    def recover_from_data_fetch_failure(self, details):
        """Recover from a data fetch failure."""
        logging.info(f"Recovering from data fetch failure: {details}")
        print(f"\nRECOVERING FROM DATA FETCH FAILURE: {details}")

        # Try to fetch data with force_refresh=True
        try:
            # Set force_refresh=True in the config temporarily
            original_setting = self.config.get('settings', {}).get('force_refresh_cache', False)
            self.config['settings']['force_refresh_cache'] = True

            # Execute the strategy
            print("ATTEMPTING TO EXECUTE STRATEGY WITH FORCE_REFRESH=TRUE")
            result = self.execute_strategy(is_recovery=True)

            # Restore original setting
            self.config['settings']['force_refresh_cache'] = original_setting

            return result
        except Exception as e:
            logging.error(f"Error during data fetch recovery: {e}")
            print(f"ERROR DURING DATA FETCH RECOVERY: {e}")
            return False

    def recover_from_strategy_failure(self, details):
        """Recover from a strategy execution failure."""
        logging.info(f"Recovering from strategy failure: {details}")
        print(f"\nRECOVERING FROM STRATEGY FAILURE: {details}")

        # Try to execute the strategy with force_refresh=True
        try:
            # Set force_refresh=True in the config temporarily
            original_setting = self.config.get('settings', {}).get('force_refresh_cache', False)
            self.config['settings']['force_refresh_cache'] = True

            # Execute the strategy
            print("ATTEMPTING TO EXECUTE STRATEGY WITH FORCE_REFRESH=TRUE")
            result = self.execute_strategy(is_recovery=True)

            # Restore original setting
            self.config['settings']['force_refresh_cache'] = original_setting

            return result
        except Exception as e:
            logging.error(f"Error during strategy recovery: {e}")
            print(f"ERROR DURING STRATEGY RECOVERY: {e}")
            return False

    def start(self):
        """Start the background service."""
        if self.is_running:
            logging.warning("Service is already running")
            return

        self.is_running = True

        # Start the network watchdog
        self.network_watchdog.start()
        logging.info("Network watchdog started")

        print("\n" + "=" * 80)
        print("BACKGROUND SERVICE STARTED WITH ENHANCED NETWORK MONITORING")
        print("=" * 80)
        print("This service includes:")
        print("✓ Continuous network connectivity monitoring")
        print("✓ Automatic recovery from network interruptions")
        print("✓ Visible reconnection notifications")
        print("✓ Telegram notifications for network status changes")
        print("✓ Detailed logging of all network events")
        print("\nTo test the network monitoring:")
        print("1. Disconnect your WiFi")
        print("2. Wait for the disconnection message")
        print("3. Reconnect your WiFi")
        print("4. Observe the reconnection message and recovery process")
        print("=" * 80)

        # Schedule strategy execution
        self._setup_schedule()

        # Start the scheduler in a separate thread
        self.scheduler_thread = threading.Thread(target=self._run_scheduler)
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()

        logging.info("Background service started")

        # Send notification with detailed information
        if self.notification_manager:
            # Get timeframe, assets, and allocation approach from config
            settings = self.config.get('settings', {})
            timeframe = settings.get('timeframe', '1d')
            # Use exchange-agnostic defaults
            from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
            exchange_id = self.config.get('exchange', 'binance')
            trading_quote = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')
            assets = settings.get('assets', [f'BTC/{trading_quote}', f'ETH/{trading_quote}', f'SOL/{trading_quote}'])

            # Get allocation approach
            allocation_approach = self.get_allocation_approach(settings)

            # Get MTPI information
            mtpi_info = self._get_mtpi_info_for_notification(settings)

            self.notification_manager.notify(
                'service_status',
                {
                    'status': 'started',
                    'mode': self.trading_config.get('mode', 'paper'),
                    'trading_enabled': str(self.trading_config.get('enabled', False)),
                    'timeframe': timeframe,
                    'assets': ', '.join(assets[:3]) + (f" and {len(assets) - 3} more" if len(assets) > 3 else ""),
                    'allocation_approach': allocation_approach,
                    'mtpi_info': mtpi_info
                }
            )

    def stop(self):
        """Stop the background service."""
        if not self.is_running:
            logging.warning("Service is not running")
            return

        self.is_running = False

        # Wait for the scheduler thread to finish
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
            self.scheduler_thread = None

        # Stop the network watchdog
        self.network_watchdog.stop()
        logging.info("Network watchdog stopped")
        print("Network watchdog stopped")

        logging.info("Background service stopped")

        # Send notification with detailed information
        if self.notification_manager:
            # Get timeframe, assets, and allocation approach from config
            settings = self.config.get('settings', {})
            timeframe = settings.get('timeframe', '1d')
            # Use exchange-agnostic defaults
            from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
            exchange_id = self.config.get('exchange', 'binance')
            trading_quote = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')
            assets = settings.get('assets', [f'BTC/{trading_quote}', f'ETH/{trading_quote}', f'SOL/{trading_quote}'])

            # Get allocation approach
            allocation_approach = self.get_allocation_approach(settings)

            # Get MTPI information
            mtpi_info = self._get_mtpi_info_for_notification(settings)

            self.notification_manager.notify(
                'service_status',
                {
                    'status': 'stopped',
                    'mode': self.trading_config.get('mode', 'paper'),
                    'trading_enabled': str(self.trading_config.get('enabled', False)),
                    'timeframe': timeframe,
                    'assets': ', '.join(assets[:3]) + (f" and {len(assets) - 3} more" if len(assets) > 3 else ""),
                    'allocation_approach': allocation_approach,
                    'mtpi_info': mtpi_info
                }
            )

    def get_allocation_approach(self, settings=None):
        """Get the allocation approach description based on settings."""
        if settings is None:
            settings = self.config.get('settings', {})

        n_assets = settings.get('n_assets', 1)
        use_weighted_allocation = settings.get('use_weighted_allocation', False)
        weights = settings.get('weights', None)

        if n_assets == 1:
            return "Best asset"
        else:
            if use_weighted_allocation:
                if weights and len(weights) >= n_assets:
                    # Format weights as percentages
                    weight_str = "-".join([f"{w*100:.0f}" for w in weights[:n_assets]])
                    return f"Weighted allocation ({weight_str} split for top {n_assets} assets)"
                else:
                    return f"Weighted allocation (top {n_assets} assets)"
            else:
                return f"Equal split (top {n_assets} assets)"

    def _get_mtpi_info_for_notification(self, settings=None, mtpi_score=None):
        """Get MTPI information for notifications."""
        if settings is None:
            settings = self.config.get('settings', {})

        use_mtpi_signal = settings.get('use_mtpi_signal', True)
        if not use_mtpi_signal:
            return "MTPI: Disabled\n"

        # Get MTPI configuration
        mtpi_config = settings.get('mtpi_indicators', {})
        enabled_indicators = mtpi_config.get('enabled_indicators', [])
        combination_method = mtpi_config.get('combination_method', 'consensus')
        long_threshold = mtpi_config.get('long_threshold', 0.1)
        short_threshold = mtpi_config.get('short_threshold', -0.1)

        # Format the MTPI info
        num_indicators = len(enabled_indicators)
        if num_indicators > 0:
            # Get current signal if available
            signal_text = ""
            score_text = ""

            if hasattr(self, 'last_mtpi_signal') and self.last_mtpi_signal is not None:
                if self.last_mtpi_signal == 1:
                    signal_text = " (🟢 BULLISH)"
                elif self.last_mtpi_signal == -1:
                    signal_text = " (🔴 BEARISH)"
                else:
                    signal_text = " (⚪ NEUTRAL)"

            # Add score information if available
            if mtpi_score is not None:
                score_text = f", score: {mtpi_score:.3f}"
            elif hasattr(self, 'last_mtpi_score') and self.last_mtpi_score is not None:
                score_text = f", score: {self.last_mtpi_score:.3f}"

            return f"MTPI: {num_indicators} indicators, {combination_method} method{signal_text}{score_text}\n"
        else:
            return "MTPI: Single indicator mode\n"

    def _get_allocation_method_name(self, settings=None):
        """Get a simple allocation method name for logging."""
        if settings is None:
            settings = self.config.get('settings', {})

        n_assets = settings.get('n_assets', 1)
        use_weighted_allocation = settings.get('use_weighted_allocation', False)
        weights = settings.get('weights', None)

        if n_assets == 1:
            return "single_asset"
        elif use_weighted_allocation:
            if weights and len(weights) >= n_assets:
                return "weighted_custom"
            else:
                return "weighted_equal"
        else:
            return "equal_split"

    def _send_mtpi_signal_notification(self, current_signal, previous_signal):
        """Send notification for MTPI signal change."""
        try:
            signal_text = {1: 'BULLISH', -1: 'BEARISH', 0: 'NEUTRAL'}

            # Use special bearish template if signal is bearish (only if MTPI is enabled)
            settings = self.config.get('settings', {})
            use_mtpi_signal = settings.get('use_mtpi_signal', True)

            if use_mtpi_signal and current_signal == -1:
                template_name = 'mtpi_signal_change_bearish'
                signal_display = f"BEARISH ❌❌❌"
            else:
                template_name = 'mtpi_signal_change'
                signal_display = signal_text.get(current_signal, 'UNKNOWN')

            # Get MTPI info for the notification
            mtpi_info = self._get_mtpi_info_for_notification(mtpi_score=self.last_mtpi_score if hasattr(self, 'last_mtpi_score') else None)

            self.notification_manager.notify(
                template_name,
                {
                    'signal': signal_display,
                    'previous_signal': signal_text.get(previous_signal, 'UNKNOWN') if previous_signal is not None else 'NONE',
                    'strategy_type': 'binance',
                    'mtpi_info': mtpi_info
                }
            )

            logging.info(f"Sent MTPI signal change notification: {previous_signal} -> {current_signal}")

        except Exception as e:
            logging.error(f"Error sending MTPI signal notification: {e}")

    def handle_shutdown(self, signum, frame):
        """Handle shutdown signals."""
        logging.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)

    def _setup_schedule(self):
        """Set up the execution schedule based on configuration."""
        # Clear existing schedule
        schedule.clear()

        # Get timeframe from config
        timeframe = self.config.get('settings', {}).get('timeframe', '1d')
        print(f"\nSetting up schedule for timeframe: {timeframe}")

        # Schedule based on timeframe
        if timeframe == '1d':
            # Run daily at 00:01 UTC (reduced from 00:05 to minimize delay)
            schedule.every().day.at("00:01").do(self.execute_strategy)
            print("Scheduled to run daily at 00:01 UTC")
        elif timeframe == '4h':
            # Run every 4 hours
            for hour in [0, 4, 8, 12, 16, 20]:
                schedule.every().day.at(f"{hour:02d}:01").do(self.execute_strategy)
            print("Scheduled to run every 4 hours at :01 minutes")
        elif timeframe == '1h':
            # Run every hour
            schedule.every().hour.at(":01").do(self.execute_strategy)
            print("Scheduled to run every hour at :01 minutes")
        elif timeframe == '1m':
            # For 1m timeframe, run every minute
            schedule.every(1).minutes.do(self.execute_strategy)
            print("Scheduled to run every minute")
        else:
            # Default: run daily
            schedule.every().day.at("00:01").do(self.execute_strategy)
            print(f"Unknown timeframe '{timeframe}', defaulting to daily at 00:01 UTC")

        # Also schedule status updates
        schedule.every().day.at("12:00").do(self.send_status_update)
        print("Scheduled status update daily at 12:00 UTC")

        # Add a weekly cleanup task for cache files
        schedule.every().sunday.at("03:00").do(self.cleanup_cache_files)
        print("Scheduled weekly cache cleanup on Sunday at 03:00 UTC")

        # Schedule daily performance report generation
        schedule.every().day.at("23:30").do(self.generate_performance_report)
        print("Scheduled daily performance report generation at 23:30 UTC")

        logging.info(f"Schedule set up for {timeframe} timeframe")

    def _run_scheduler(self):
        """Run the scheduler loop."""
        # Run immediately on start
        print("\nRunning strategy immediately on start...")
        self.execute_strategy()

        # Then run according to schedule
        print("\nScheduler loop started - waiting for scheduled executions")

        # Get timeframe from config
        timeframe = self.config.get('settings', {}).get('timeframe', '1d')

        # Determine sleep time based on timeframe
        if timeframe == '1m':
            sleep_time = 1  # Check every second for 1m timeframe
        else:
            sleep_time = 60  # Check every minute for other timeframes

        print(f"Checking for scheduled tasks every {sleep_time} second(s)")

        # Initialize last status check time
        last_status_check = time.time()
        status_check_interval = 300  # Check and display status every 5 minutes

        while self.is_running:
            # Only run scheduled tasks if network is connected
            if self.network_watchdog.is_connected:
                schedule.run_pending()
            else:
                # If network is down, log it but don't try to run tasks
                if self.last_network_status:  # Only log when status changes
                    logging.warning("Network is down, skipping scheduled tasks")
                    print("\nNETWORK IS DOWN, SKIPPING SCHEDULED TASKS")
                    self.last_network_status = False

            # Update network status if it changed
            if not self.last_network_status and self.network_watchdog.is_connected:
                logging.info("Network is back up, resuming scheduled tasks")
                print("\nNETWORK IS BACK UP, RESUMING SCHEDULED TASKS")
                self.last_network_status = True

                # Check for missed executions
                self.check_missed_executions()

            # Periodically display network status (every 5 minutes)
            current_time = time.time()
            if current_time - last_status_check >= status_check_interval:
                status = self.network_watchdog.get_connection_status()
                print(f"\nNETWORK STATUS CHECK: {'CONNECTED' if status['is_connected'] else 'DISCONNECTED'}")
                print(f"In critical window: {'YES' if status['in_critical_window'] else 'NO'}")
                print(f"Consecutive failures: {status['consecutive_failures']}")

                # Reset the timer
                last_status_check = current_time

            time.sleep(sleep_time)

    def execute_strategy(self, is_recovery=False):
        """Execute the strategy and handle results."""
        try:
            # Check network connectivity first
            if not self.network_watchdog.is_connected and not is_recovery:
                logging.warning("Network is down, skipping strategy execution")
                print("NETWORK IS DOWN, SKIPPING STRATEGY EXECUTION")
                return False

            self.execution_count += 1
            execution_start_time = datetime.now()

            logging.info(f"Executing strategy (run #{self.execution_count})...")
            print("\n" + "=" * 50)
            print(f"EXECUTING STRATEGY (RUN #{self.execution_count}) - {execution_start_time}")
            print("=" * 50)

            # Reset daily trade counters if this is the first run of the day
            if hasattr(self, 'last_execution_time') and self.last_execution_time:
                last_date = self.last_execution_time.date()
                current_date = datetime.now().date()
                if current_date > last_date:
                    logging.info(f"New day detected. Resetting daily trade counters.")
                    print("NEW DAY DETECTED - RESETTING DAILY TRADE COUNTERS")
                    self.trading_executor.risk_manager.reset_daily_trade_counters()

            # Always reset daily trade counters before each strategy execution
            # This ensures that the daily trade limits don't persist between strategy runs
            #
            # IMPORTANT: This is necessary to prevent issues where high-scoring assets like BTC
            # are rejected due to hitting daily trade limits from previous runs.
            # Each strategy execution should have a fresh set of trade counters to ensure
            # that the highest-scoring assets are always considered first.
            logging.info("Resetting daily trade counters for this strategy execution")
            print("RESETTING DAILY TRADE COUNTERS FOR THIS STRATEGY EXECUTION")
            self.trading_executor.risk_manager.reset_daily_trade_counters()

            # Log current daily trade counts for diagnostic purposes
            print("\nCURRENT DAILY TRADE COUNTS:")
            self.trading_executor.risk_manager.log_daily_trade_counts()

            # Get the max daily trades limit from config for reference
            max_daily_trades = self.trading_config.get('risk_management', {}).get('max_daily_trades', 5)
            print(f"Maximum daily trades limit: {max_daily_trades}")

            # Get current counts
            daily_counts = self.trading_executor.risk_manager.get_daily_trade_counts()
            if daily_counts:
                for symbol, count in sorted(daily_counts.items(), key=lambda x: x[1], reverse=True):
                    status = "LIMIT REACHED" if count >= max_daily_trades else f"{count}/{max_daily_trades}"
                    print(f"  {symbol}: {status}")
            else:
                print("  No trades recorded today")

            # Check BTC with the appropriate quote currency
            settings = self.config.get('settings', {})
            # Use exchange-agnostic defaults
            from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
            exchange_id = self.config.get('exchange', 'binance')
            trading_quote = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')
            trading_assets = settings.get('assets', [f'BTC/{trading_quote}'])
            btc_symbol = None
            for asset in trading_assets:
                if asset.startswith('BTC/'):
                    btc_symbol = asset
                    break

            if btc_symbol:
                btc_count = daily_counts.get(btc_symbol, 0)
                btc_status = "LIMIT REACHED" if btc_count >= max_daily_trades else f"{btc_count}/{max_daily_trades}"
                print(f"\n{btc_symbol} daily trade count: {btc_status}")

                # Check if BTC would hit daily trade limit without incrementing the counter
                would_hit_limit = not self.trading_executor.risk_manager.check_daily_trade_limit(btc_symbol, increment=False)
                print(f"Would {btc_symbol} hit daily trade limit? {'YES' if would_hit_limit else 'NO'}")
            else:
                print(f"\nNo BTC asset found in trading assets")

            # Get strategy parameters from config
            settings = self.config.get('settings', {})

            # Get the timeframe from settings
            timeframe = settings.get('timeframe', '1d')
            print(f"TIMEFRAME: {timeframe}")

            # Create a candle-based timestamp for the snapshot
            # This will create one image per candle close
            now = datetime.now()
            print(f"CURRENT TIME: {now}")

            # Format the timestamp based on the timeframe
            if timeframe == '1d':
                # For daily timeframe, use YYYYMMDD format
                candle_timestamp = now.strftime("%Y%m%d")
            elif timeframe == '4h':
                # For 4h timeframe, use YYYYMMDD_HH format where HH is 00, 04, 08, 12, 16, or 20
                hour = (now.hour // 4) * 4  # Round down to nearest 4-hour interval
                candle_timestamp = f"{now.strftime('%Y%m%d')}_{hour:02d}"
            elif timeframe == '1h':
                # For 1h timeframe, use YYYYMMDD_HH format
                candle_timestamp = now.strftime("%Y%m%d_%H")
            elif timeframe == '1m':
                # For 1m timeframe, use YYYYMMDD_HHMM format
                candle_timestamp = now.strftime("%Y%m%d_%H%M")
            else:
                # Default format for other timeframes
                candle_timestamp = now.strftime("%Y%m%d_%H%M")

            logging.info(f"Creating snapshot for candle timestamp: {candle_timestamp}")
            print(f"CANDLE TIMESTAMP: {candle_timestamp}")

            # Send notification
            if self.notification_manager:
                # Get allocation approach
                allocation_approach = self.get_allocation_approach(settings)
                print("Sending strategy execution start notification...")

                # Include previous values if available
                previous_best_asset = "Analyzing..." if self.last_best_asset is None else f"{self.last_best_asset} (previous)"

                # Get MTPI information for notification
                mtpi_info = self._get_mtpi_info_for_notification(settings)

                self.notification_manager.notify(
                    'strategy_execution',
                    {
                        'status': 'started',
                        'best_asset': previous_best_asset,
                        'mtpi_info': mtpi_info,
                        'allocation_approach': allocation_approach,
                        'assets_traded_str': '',  # Empty string for the start notification
                        'asset_scores_str': '',   # Empty string for asset scores in start notification
                        'market_warning': ''      # Empty string for market warning
                    }
                )

            # No longer creating snapshot directories

            # Get the trend method from config
            trend_method = settings.get('trend_method', 'RSI')
            logging.info(f"Using trend method '{trend_method}' for strategy execution")
            print(f"TREND METHOD: {trend_method}")

            # Get the configured start date for analysis
            # Use UTC timezone to ensure consistency with data timestamps
            from datetime import timezone
            configured_start_date = settings.get('start_date', '2024-01-01')
            current_date = datetime.now(tz=timezone.utc).strftime("%Y-%m-%d")

            # For strategy execution, we'll use the configured start date to ensure we have complete candles
            # This ensures proper strategy execution with sufficient historical data
            if timeframe == '1d':
                analysis_start_date = configured_start_date
                logging.info(f"Using configured start date for 1d timeframe: {analysis_start_date}")
                print(f"ANALYSIS START DATE: {analysis_start_date} (from configuration)")
            else:
                analysis_start_date = current_date
                logging.info(f"Using current date as analysis start date: {analysis_start_date}")
                print(f"ANALYSIS START DATE: {analysis_start_date} (current date)")

            # For performance metrics tracking, we'll use a more recent date
            # This ensures metrics are tracked from a recent point rather than from the beginning
            # Calculate a date 7 days ago for performance tracking
            performance_tracking_date = (datetime.now(tz=timezone.utc) - timedelta(days=7)).strftime("%Y-%m-%d")
            logging.info(f"Using recent date for performance tracking: {performance_tracking_date}")
            print(f"PERFORMANCE TRACKING DATE: {performance_tracking_date} (7 days ago)")

            # Calculate and display indicator warmup periods
            # Standard warmup periods: 120 days for both asset data and MTPI
            asset_warmup_days = 120
            mtpi_warmup_days = 120  # Increased to 120 days for proper MTPI crossover detection

            # Adjust warmup days based on timeframe
            if timeframe == '1m':
                asset_warmup_days = 90  # More days for higher frequency data
            elif timeframe.endswith('h'):  # hourly timeframes
                # Extract the number from timeframe (e.g., '4h' -> 4)
                import re
                match = re.match(r'(\d+)h', timeframe)
                if match:
                    hours = int(match.group(1))
                    # For 1h -> 90 days, 4h -> 75 days, 12h -> 60 days
                    asset_warmup_days = max(60, 90 - (hours * 2.5))

            # Calculate the actual dates
            analysis_date_obj = datetime.strptime(analysis_start_date, '%Y-%m-%d')
            asset_warmup_start = (analysis_date_obj - timedelta(days=asset_warmup_days)).strftime('%Y-%m-%d')
            mtpi_warmup_start = (analysis_date_obj - timedelta(days=mtpi_warmup_days)).strftime('%Y-%m-%d')

            print(f"INDICATOR WARMUP PERIODS:")
            print(f"  - Asset data warmup: {asset_warmup_days} days (starting from {asset_warmup_start})")
            print(f"  - MTPI signal warmup: {mtpi_warmup_days} days (starting from {mtpi_warmup_start})")

            # Log the assets being used
            # Use exchange-agnostic defaults
            from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
            exchange_id = self.config.get('exchange', 'binance')
            trading_quote = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')
            assets = settings.get('assets', [f'BTC/{trading_quote}', f'ETH/{trading_quote}', f'SOL/{trading_quote}'])
            print(f"ASSETS: {', '.join(assets)}")

            # Log allocation approach
            n_assets = settings.get('n_assets', 1)
            use_weighted_allocation = settings.get('use_weighted_allocation', False)
            if n_assets == 1:
                allocation_str = "Best asset"
            else:
                if use_weighted_allocation:
                    weights = settings.get('weights', [0.7, 0.2, 0.1])
                    weights_str = ", ".join([f"{w*100:.1f}%" for w in weights[:n_assets]])
                    allocation_str = f"Weighted allocation ({weights_str})"
                else:
                    allocation_str = f"Equal split (top {n_assets} assets)"
            print(f"ALLOCATION APPROACH: {allocation_str}")

            print("\nFETCHING DATA AND RUNNING STRATEGY...")
            print("1. Fetching latest price data from exchange")
            print("2. Calculating MTPI signals")
            print("3. Calculating asset scores")
            print("4. Determining best assets")
            print("5. Generating strategy results")
            print("6. Creating snapshot image")

            # Determine if we should use incremental fetching
            use_incremental = not settings.get('force_refresh_cache', False)

            # Log the data fetching approach
            if use_incremental:
                print("USING REAL-TIME DATA FETCHING - Only fetching new data since last cached timestamp")
                logging.info("Using real-time data fetching - only fetching new data since last cached timestamp")
            else:
                print("USING FULL DATA REFRESH - Fetching all data from the beginning")
                logging.info("Using full data refresh - fetching all data from the beginning")

            # Pre-fetch data incrementally if enabled
            if use_incremental:
                try:
                    # Get the exchange ID from config
                    exchange_id = self.config.get('exchange', 'binance')

                    # Get the trend assets for data fetching (USDT pairs for better data coverage)
                    trend_assets = settings.get('trend_assets', settings.get('assets', ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']))
                    timeframe = settings.get('timeframe', '1d')

                    # Fetch real-time data for trend detection assets
                    print("PRE-FETCHING REAL-TIME DATA FOR TREND DETECTION (USDT pairs)...")
                    data_dict = fetch_real_time_data(
                        exchange_id=exchange_id,
                        symbols=trend_assets,
                        timeframe=timeframe,
                        use_cache=True
                    )

                    # Log the results
                    if data_dict:
                        print(f"SUCCESSFULLY PRE-FETCHED DATA FOR {len(data_dict)} ASSETS")
                        for symbol, df in data_dict.items():
                            if not df.empty:
                                print(f"  - {symbol}: {len(df)} candles from {df.index.min().date()} to {df.index.max().date()}")
                    else:
                        print("NO DATA WAS PRE-FETCHED")
                except Exception as e:
                    logging.error(f"Error pre-fetching real-time data: {e}")
                    print(f"ERROR PRE-FETCHING REAL-TIME DATA: {e}")
                    print("FALLING BACK TO STANDARD DATA FETCHING")
                    use_incremental = False

            # Get the trend assets for analysis and trading assets for execution
            trend_assets = settings.get('trend_assets', settings.get('assets', ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']))
            # Use exchange-agnostic defaults
            from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
            exchange_id = self.config.get('exchange', 'binance')
            trading_quote = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')
            trading_assets = settings.get('assets', [f'BTC/{trading_quote}', f'ETH/{trading_quote}', f'SOL/{trading_quote}'])
            asset_count = len(trend_assets)

            # Detect the quote currency from trading assets
            trading_quote_currency = 'USDC'  # Default
            if trading_assets and len(trading_assets) > 0:
                first_trading_asset = trading_assets[0]
                if '/' in first_trading_asset:
                    trading_quote_currency = first_trading_asset.split('/')[1]

            logging.info(f"Using {asset_count} trend assets (USDT) for analysis and {len(trading_assets)} trading assets ({trading_quote_currency}) for execution")
            print(f"TREND ASSET COUNT: {asset_count}")
            print(f"TRADING ASSET COUNT: {len(trading_assets)}")

            # Load MTPI multi-indicator configuration from YAML
            mtpi_config = settings.get('mtpi_indicators', {})
            enabled_indicators = mtpi_config.get('enabled_indicators', ['pgo', 'bollinger_bands'])
            combination_method = mtpi_config.get('combination_method', 'consensus')
            long_threshold = mtpi_config.get('long_threshold', 0.1)
            short_threshold = mtpi_config.get('short_threshold', -0.1)

            num_indicators = len(enabled_indicators)
            logging.info(f"MTPI Multi-Indicator Configuration:")
            logging.info(f"  - Number of indicators: {num_indicators}")
            logging.info(f"  - Enabled indicators: {enabled_indicators}")
            logging.info(f"  - Combination method: {combination_method}")
            logging.info(f"  - Long threshold: {long_threshold}")
            logging.info(f"  - Short threshold: {short_threshold}")

            print(f"MTPI MULTI-INDICATOR SETUP:")
            print(f"  - Number of indicators: {num_indicators}")
            print(f"  - Indicators: {', '.join(enabled_indicators)}")
            print(f"  - Method: {combination_method}")
            print(f"  - Thresholds: {long_threshold}/{short_threshold}")

            # 🚨 DEBUG: Log asset orders before strategy execution
            logging.error(f"🚨 BACKGROUND SERVICE DEBUG - trend_assets order: {trend_assets}")
            logging.error(f"🚨 BACKGROUND SERVICE DEBUG - trading_assets order: {trading_assets}")
            logging.error(f"🚨 BACKGROUND SERVICE DEBUG - BTC position in trend_assets: {trend_assets.index('BTC/USDT') + 1 if 'BTC/USDT' in trend_assets else 'NOT FOUND'}")
            logging.error(f"🚨 BACKGROUND SERVICE DEBUG - TRX position in trend_assets: {trend_assets.index('TRX/USDT') + 1 if 'TRX/USDT' in trend_assets else 'NOT FOUND'}")

            # Run the strategy with multi-indicator MTPI
            # Use trend_assets (USDT) for analysis, trading_assets (USDC) will be used for execution
            results = run_strategy_for_web(
                use_mtpi_signal=settings.get('use_mtpi_signal', True),
                mtpi_timeframe=settings.get('mtpi_timeframe', '1d'),
                # Multi-indicator MTPI parameters
                mtpi_indicators=enabled_indicators,
                mtpi_combination_method=combination_method,
                mtpi_long_threshold=long_threshold,
                mtpi_short_threshold=short_threshold,
                timeframe=settings.get('timeframe', '1d'),
                analysis_start_date=analysis_start_date,  # Use the determined analysis start date for full strategy calculation
                selected_assets=trend_assets,  # Use USDT pairs for trend detection
                trading_assets=trading_assets,  # Pass USDC pairs for trading execution
                n_assets=settings.get('n_assets', 1),
                use_weighted_allocation=settings.get('use_weighted_allocation', False),
                weights=settings.get('weights', None),
                enable_rebalancing=settings.get('enable_rebalancing', False),
                rebalance_threshold=settings.get('rebalance_threshold', 0.05),
                use_cache=True,
                force_refresh_cache=not use_incremental,  # Only force refresh if not using incremental fetching
                save_snapshot=False,  # Don't save snapshots to Live_Strat_Progress
                snapshot_prefix=candle_timestamp,  # Keep timestamp for potential future use
                trend_method=trend_method,  # Pass the trend method explicitly
                plot_start_date=performance_tracking_date,  # Use the performance tracking date for plotting
                save_metrics_to_csv=True,  # Save metrics to CSV
                metrics_dir=self.metrics_dir,  # Use the metrics directory from the service
                run_id=self.run_id,  # Pass the run ID to create a new file for each run
                asset_count=asset_count,  # Pass the asset count for the filename
                ratio_calculation=settings.get('ratio_calculation', 'manual_inversion'),  # Add ratio calculation method
                config_path=self.config_path  # Pass the config path to ensure correct configuration is used
            )

            if not results or 'error' in results:
                error_msg = results.get('error', 'Unknown error') if results else 'No results returned'
                logging.error(f"Strategy execution failed: {error_msg}")
                print(f"\nERROR: Strategy execution failed: {error_msg}")

                # Send error notification
                if self.notification_manager:
                    print("Sending error notification...")
                    self.notification_manager.notify(
                        'error_alert',
                        {
                            'error': error_msg,
                            'component': 'strategy_execution'
                        }
                    )
                return

            # Extract key results
            mtpi_signal = None
            best_asset = None
            assets_held = {}

            # Get MTPI signal
            if 'mtpi_signals' in results and results['mtpi_signals'] is not None:
                if isinstance(results['mtpi_signals'], pd.Series):
                    mtpi_signal = results['mtpi_signals'].iloc[-1] if not results['mtpi_signals'].empty else None
                elif isinstance(results['mtpi_signals'], dict) and 'latest' in results['mtpi_signals']:
                    mtpi_signal = results['mtpi_signals']['latest']

            # If MTPI is disabled, use a default bullish signal (1) to allow trading
            use_mtpi_signal = settings.get('use_mtpi_signal', True)
            if not use_mtpi_signal and mtpi_signal is None:
                mtpi_signal = 1  # Default to bullish signal when MTPI is disabled
                logging.info("MTPI disabled - using default bullish signal (1) for trading execution")

            # 🚨 DEBUG: Log all results keys to see what's available
            logging.error(f"🚨 STRATEGY RESULTS DEBUG - Available keys: {list(results.keys()) if results else 'No results'}")

            # Get best asset and assets held
            if 'best_asset_series' in results and results['best_asset_series'] is not None:
                if isinstance(results['best_asset_series'], pd.Series):
                    best_asset_series = results['best_asset_series']
                    if not best_asset_series.empty:
                        best_asset = best_asset_series.iloc[-1]
                        logging.error(f"🚨 ASSET SELECTION DEBUG - Extracted best_asset from best_asset_series: {best_asset}")
                        logging.error(f"🚨 ASSET SELECTION DEBUG - Last 5 entries in best_asset_series: {best_asset_series.tail()}")

                        # 🚨 DEBUG: Check if we can access the scores that led to this selection
                        if 'latest_scores' in results:
                            latest_scores = results['latest_scores']
                            logging.error(f"🚨 ASSET SELECTION DEBUG - Latest scores available: {latest_scores}")
                            logging.error(f"🚨 ASSET SELECTION DEBUG - Scores dictionary keys order: {list(latest_scores.keys())}")

                            # Check BTC and TRX scores specifically
                            btc_score = latest_scores.get('BTC/EUR', 'NOT FOUND')
                            trx_score = latest_scores.get('TRX/EUR', 'NOT FOUND')
                            logging.error(f"🚨 ASSET SELECTION DEBUG - BTC/EUR score: {btc_score}")
                            logging.error(f"🚨 ASSET SELECTION DEBUG - TRX/EUR score: {trx_score}")

                            # Find max score and tied assets
                            if isinstance(latest_scores, dict):
                                max_score = max(latest_scores.values()) if latest_scores else 0
                                tied_assets = [asset for asset, score in latest_scores.items() if score == max_score]
                                logging.error(f"🚨 ASSET SELECTION DEBUG - Maximum score: {max_score}")
                                logging.error(f"🚨 ASSET SELECTION DEBUG - Assets with max score: {tied_assets}")

                                if len(tied_assets) > 1:
                                    logging.error(f"🚨 TIE DETECTED - {len(tied_assets)} assets tied at score {max_score}")
                                    logging.error(f"🚨 TIE DETECTED - Tied assets: {tied_assets}")
                                    logging.error(f"🚨 TIE DETECTED - Selected asset: {best_asset}")

                                    # Check dictionary order for tie-breaking
                                    scores_keys_list = list(latest_scores.keys())
                                    logging.error(f"🚨 TIE DETECTED - Dictionary keys order: {scores_keys_list}")

                                    if 'BTC/EUR' in tied_assets and 'TRX/EUR' in tied_assets:
                                        btc_pos = scores_keys_list.index('BTC/EUR') + 1
                                        trx_pos = scores_keys_list.index('TRX/EUR') + 1
                                        logging.error(f"🚨 BTC vs TRX TIE - BTC position: {btc_pos}, TRX position: {trx_pos}")
                                        logging.error(f"🚨 BTC vs TRX TIE - BTC comes first: {btc_pos < trx_pos}")

                                        if best_asset == 'BTC/EUR':
                                            logging.error(f"🚨 BTC vs TRX TIE - ✅ BTC was selected (correct)")
                                        elif best_asset == 'TRX/EUR':
                                            logging.error(f"🚨 BTC vs TRX TIE - ❌ TRX was selected (BUG!)")
                                        else:
                                            logging.error(f"🚨 BTC vs TRX TIE - ? {best_asset} was selected")
                        else:
                            logging.error(f"🚨 ASSET SELECTION DEBUG - No 'latest_scores' found in results")

                        # Check if best_asset contains multiple assets (comma-separated)
                        if best_asset and ',' in best_asset:
                            # This is a multi-asset strategy result
                            assets_list = [a.strip() for a in best_asset.split(',')]

                            # Get the actual highest-scoring asset from the performance metrics
                            highest_scoring_asset = None
                            if 'performance_metrics' in results and 'latest_scores' in results['performance_metrics']:
                                latest_scores = results['performance_metrics']['latest_scores']
                                if latest_scores:
                                    # Find the asset with the highest score
                                    highest_scoring_asset = max(latest_scores.items(), key=lambda x: x[1])[0]
                                    logging.info(f"Found highest scoring asset from metrics: {highest_scoring_asset} with score {latest_scores[highest_scoring_asset]}")

                            # Use the highest scoring asset if available, otherwise use the first one
                            if highest_scoring_asset and highest_scoring_asset in assets_list:
                                best_asset = highest_scoring_asset
                                logging.info(f"Using highest scoring asset for best_asset: {best_asset}")
                            else:
                                # For backward compatibility, set best_asset to the first one
                                best_asset = assets_list[0] if assets_list else None
                                logging.info(f"Using first asset in list for best_asset: {best_asset}")
                elif isinstance(results['best_asset_series'], dict) and 'latest' in results['best_asset_series']:
                    best_asset = results['best_asset_series']['latest']

            # Get assets held with weights if available
            if 'assets_held_df' in results and results['assets_held_df'] is not None:
                assets_held_df = results['assets_held_df']
                logging.debug(f"Assets held DataFrame: {assets_held_df}")

                if not assets_held_df.empty:
                    # Extract assets with non-zero allocation from the last row
                    n_assets = settings.get('n_assets', 1)
                    use_weighted_allocation = settings.get('use_weighted_allocation', False)
                    weights = settings.get('weights', None)

                    # Debug log the last row of assets_held_df
                    last_row = assets_held_df.iloc[-1]
                    logging.debug(f"Last row of assets_held_df: {last_row}")

                    # Check if 'assets_held' column exists
                    if 'assets_held' in assets_held_df.columns:
                        assets_held_last = assets_held_df['assets_held'].iloc[-1]
                        logging.info(f"Assets held in last row: {assets_held_last}")
                    else:
                        logging.warning(f"No 'assets_held' column found in assets_held_df. Columns: {assets_held_df.columns}")
                        logging.info(f"Last row columns: {last_row.index.tolist()}")

                    # IMPROVED ASSET EXTRACTION LOGIC
                    assets_list = []

                    if n_assets > 1:
                        # For multi-asset strategy
                        # Try to extract assets from assets_held_df first
                        if 'assets_held' in assets_held_df.columns:
                            assets_held_last = assets_held_df['assets_held'].iloc[-1]

                            # Parse the assets_held string if it contains multiple assets
                            if isinstance(assets_held_last, str) and ',' in assets_held_last:
                                assets_list = [a.strip() for a in assets_held_last.split(',')]
                                logging.info(f"Parsed assets list from string: {assets_list}")
                            elif isinstance(assets_held_last, list):
                                assets_list = assets_held_last
                                logging.info(f"Extracted assets from assets_held_df: {assets_list}")
                            elif assets_held_last:  # Single asset
                                assets_list = [assets_held_last]
                                logging.info(f"Single asset from assets_held column: {assets_list}")
                        else:
                            # Extract assets directly from the dataframe
                            # Get the list of available assets from config
                            available_assets = settings.get('assets', [])
                            logging.info(f"Available assets from config: {available_assets}")

                            # Check if these assets exist as columns in the dataframe
                            asset_columns = [col for col in last_row.index if col in available_assets]
                            logging.info(f"Asset columns found in dataframe: {asset_columns}")

                            if asset_columns:
                                # Get non-zero allocations
                                for asset in asset_columns:
                                    if last_row[asset] > 0:
                                        assets_list.append(asset)
                                logging.info(f"Assets with non-zero allocation: {assets_list}")

                                # If we still don't have enough assets, take the top n by value
                                if len(assets_list) < n_assets and len(asset_columns) >= n_assets:
                                    # Sort assets by their values in descending order
                                    sorted_assets = sorted(asset_columns, key=lambda x: last_row[x], reverse=True)
                                    assets_list = sorted_assets[:n_assets]
                                    logging.info(f"Top {n_assets} assets by value: {assets_list}")
                            else:
                                # Fallback to best_asset
                                if best_asset:
                                    if ',' in best_asset:
                                        assets_list = [a.strip() for a in best_asset.split(',')]
                                        logging.info(f"Using best_asset (comma-separated) for assets list: {assets_list}")
                                    else:
                                        assets_list = [best_asset]
                                        logging.info(f"Using best_asset for assets list: {assets_list}")
                                else:
                                    logging.warning("No best_asset available for fallback")

                        # If we still don't have enough assets, add more from available_assets
                        if len(assets_list) < n_assets:
                            available_assets = settings.get('assets', [])
                            remaining_assets = [a for a in available_assets if a not in assets_list]

                            # Add remaining assets until we reach n_assets
                            assets_to_add = min(n_assets - len(assets_list), len(remaining_assets))
                            if assets_to_add > 0:
                                assets_list.extend(remaining_assets[:assets_to_add])
                                logging.info(f"Added additional assets to reach n_assets={n_assets}: {assets_list}")

                        # Create the assets_held dictionary with appropriate weights
                        if len(assets_list) > 0:
                            # Get the latest scores to ensure proper ordering
                            if 'performance_metrics' in results and 'latest_scores' in results['performance_metrics']:
                                latest_scores = results['performance_metrics']['latest_scores']
                                if latest_scores:
                                    # IMPORTANT: Instead of just sorting the existing assets_list,
                                    # we need to select the top N assets by score from ALL available assets
                                    # This ensures we're always using the highest-scoring assets

                                    # Sort ALL assets by score in descending order
                                    all_assets_with_scores = sorted(latest_scores.items(), key=lambda x: x[1], reverse=True)
                                    logging.info(f"All assets sorted by score: {all_assets_with_scores}")

                                    # Select the top N assets by score
                                    top_n_assets = [asset for asset, _ in all_assets_with_scores[:n_assets]]
                                    logging.info(f"Selected top {n_assets} assets by score: {top_n_assets}")

                                    # Replace the assets_list with the top N assets
                                    assets_list = top_n_assets
                                    logging.info(f"Updated assets list with top-scoring assets: {assets_list}")

                            if use_weighted_allocation and weights and len(weights) >= len(assets_list):
                                # Use the configured weights with assets sorted by score
                                assets_held = {asset: weights[i] for i, asset in enumerate(assets_list) if i < len(weights)}
                                logging.info(f"Using weighted allocation with configured weights: {assets_held}")
                            else:
                                # Equal allocation
                                weight = 1.0 / len(assets_list)
                                assets_held = {asset: weight for asset in assets_list}
                                logging.info(f"Using equal allocation for {len(assets_list)} assets: {assets_held}")
                        else:
                            logging.warning("No assets found in assets list")
                    else:
                        # For single asset strategy
                        if best_asset:
                            assets_held = {best_asset: 1.0}  # 100% allocation to the best asset
                            logging.info(f"Single asset strategy with best asset: {best_asset}")

            # If no assets_held were extracted but we have a best_asset, use that
            if not assets_held and best_asset:
                assets_held = {best_asset: 1.0}  # 100% allocation to the best asset

            # 🚨 DEBUG: Final asset selection summary
            logging.error(f"🚨 FINAL ASSET SELECTION SUMMARY:")
            logging.error(f"🚨   - Best asset selected: {best_asset}")
            logging.error(f"🚨   - Assets held: {assets_held}")
            logging.error(f"🚨   - MTPI signal: {mtpi_signal}")
            logging.error(f"🚨   - Use MTPI signal: {use_mtpi_signal}")

            if best_asset == 'TRX/EUR':
                logging.error(f"🚨 ❌ TRX WAS SELECTED - INVESTIGATING WHY!")
            elif best_asset == 'BTC/EUR':
                logging.error(f"🚨 ✅ BTC WAS SELECTED - EXPECTED BEHAVIOR")
            else:
                logging.error(f"🚨 ? {best_asset} WAS SELECTED")

            # Check for signal changes
            mtpi_signal_changed = (self.last_mtpi_signal is not None and
                                  self.last_mtpi_signal != mtpi_signal)
            best_asset_changed = (self.last_best_asset is not None and
                                 self.last_best_asset != best_asset)

            # Send notifications for changes
            if mtpi_signal_changed and self.notification_manager:
                self._send_mtpi_signal_notification(mtpi_signal, self.last_mtpi_signal)

            if best_asset_changed and self.notification_manager:
                self.notification_manager.notify(
                    'asset_rotation',
                    {
                        'best_asset': best_asset,
                        'previous_best_asset': self.last_best_asset,
                        'new_asset': best_asset  # Add the new_asset field that the template expects
                    }
                )

            # Execute trades if trading is enabled
            # Allow trading when MTPI is disabled (mtpi_signal is None) or when MTPI signal is available
            use_mtpi_signal = settings.get('use_mtpi_signal', True)
            should_execute_trades = (self.trading_config.get('enabled', False) and
                                   (not use_mtpi_signal or mtpi_signal is not None))

            if should_execute_trades:
                # Determine which trading method to use based on the number of assets
                n_assets = settings.get('n_assets', 1)

                if n_assets > 1 and assets_held:
                    # Use multi-asset trading for n_assets > 1, even if only one asset is currently held
                    # This ensures we use the multi-asset trading path when n_assets > 1 is configured
                    logging.info(f"Executing multi-asset strategy with {len(assets_held)} assets: {assets_held}")
                    print("\n" + "=" * 70)
                    print(f"EXECUTING MULTI-ASSET STRATEGY WITH {len(assets_held)} ASSETS")
                    print("=" * 70)

                    # Print the assets and their weights
                    print("\nASSETS WITH WEIGHTS:")
                    for asset, weight in assets_held.items():
                        print(f"  - {asset}: {weight*100:.2f}%")

                    # Get the latest scores to pass to the executor for better replacement logic
                    latest_scores = None
                    if 'performance_metrics' in results and 'latest_scores' in results['performance_metrics']:
                        latest_scores = results['performance_metrics']['latest_scores']
                        logging.info(f"Passing {len(latest_scores)} asset scores to executor for replacement logic")

                        # Print the asset scores
                        print("\nASSET SCORES:")
                        if latest_scores:
                            # Sort by score in descending order
                            sorted_scores = sorted(latest_scores.items(), key=lambda x: x[1], reverse=True)
                            for asset, score in sorted_scores:
                                in_portfolio = "✓" if asset in assets_held else " "
                                print(f"  {in_portfolio} {asset}: {score}")
                        else:
                            print("  No asset scores available")

                    # Get available balance before trades
                    # Detect quote currency from the first asset
                    # Use exchange-agnostic default
                    from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
                    exchange_id = self.config.get('exchange', 'binance')
                    quote_currency = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')
                    if assets_held:
                        first_asset = list(assets_held.keys())[0]
                        if '/' in first_asset:
                            quote_currency = first_asset.split('/')[1]

                    if self.trading_config.get('mode') == 'paper':
                        available_balance = self.trading_executor.paper_trading.get_balance().get(quote_currency, 0.0)
                    else:
                        available_balance = self.trading_executor.account_manager.get_balance(quote_currency)

                    print(f"\nAVAILABLE BALANCE BEFORE TRADES: {available_balance:.8f} {quote_currency}")

                    # Calculate and print the target allocation amounts
                    print("\nTARGET ALLOCATION AMOUNTS:")
                    for asset, weight in assets_held.items():
                        target_amount = available_balance * weight
                        print(f"  - {asset}: {weight*100:.2f}% = {target_amount:.8f} {quote_currency}")

                        # Get current price for this asset
                        price = self.trading_executor.get_current_price(asset)
                        if price:
                            # Calculate base amount (quantity)
                            base_amount = target_amount / price
                            print(f"    Price: {price:.8f} {quote_currency}")
                            print(f"    Raw quantity: {base_amount:.8f} units")

                            # Get precision info for this asset
                            from src.utils.precision import get_asset_precision
                            precision_info = get_asset_precision(asset, trading_config=self.trading_config)
                            print(f"    Precision requirements: min_amount={precision_info['min_amount']}, amount_precision={precision_info['amount']}")

                            # Calculate adjusted amount
                            from src.utils.precision import adjust_amount_for_precision
                            adjusted_amount = adjust_amount_for_precision(
                                base_amount,
                                asset,
                                price=price,
                                is_buy=True,
                                trading_config=self.trading_config
                            )

                            print(f"    Adjusted quantity: {adjusted_amount:.8f} units")
                            if adjusted_amount == 0:
                                print(f"    WARNING: Adjusted amount is ZERO - this trade will fail!")
                                print(f"    Reason: Amount is below minimum order size of {precision_info['min_amount']}")

                    print("\nEXECUTING TRADES...")
                    trade_result = self.trading_executor.execute_multi_asset_strategy(
                        assets_held, mtpi_signal, asset_scores=latest_scores
                    )

                    # Print detailed trade results
                    print("\n" + "=" * 70)
                    print("TRADE EXECUTION RESULTS")
                    print("=" * 70)

                    # Print overall result
                    success = trade_result.get('success', False)
                    print(f"\nOVERALL RESULT: {'SUCCESS' if success else 'FAILURE'}")
                    if not success and 'reason' in trade_result:
                        print(f"REASON: {trade_result['reason']}")

                    # Print individual trade results
                    print("\nINDIVIDUAL TRADE RESULTS:")
                    for i, trade in enumerate(trade_result.get('trades', []), 1):
                        symbol = trade.get('symbol', 'UNKNOWN')
                        side = trade.get('side', 'UNKNOWN')
                        amount = trade.get('amount', 0)
                        price = trade.get('price', 0)
                        success = trade.get('success', False)

                        print(f"\nTRADE #{i}: {side.upper()} {symbol}")
                        print(f"  Success: {'YES' if success else 'NO'}")

                        if success:
                            print(f"  Amount: {amount:.8f}")
                            print(f"  Price: {price:.8f}")
                            print(f"  Value: {amount * price:.8f} {quote_currency}")

                            # Extract order details
                            order = trade.get('order', {})
                            filled = order.get('filled', amount)

                            # Extract fee details
                            fee = 'Unknown'
                            fee_cost = 0
                            if 'fee' in order and order['fee'] is not None:
                                fee_cost = order['fee'].get('cost', 0)
                                fee_currency = order['fee'].get('currency', '')
                                fee = f"{fee_cost} {fee_currency}" if fee_cost > 0 else 'None'

                            print(f"  Filled: {filled:.8f}")
                            print(f"  Fee: {fee}")
                        else:
                            reason = trade.get('reason', 'Unknown reason')
                            print(f"  Failed: {reason}")

                    # Print errors
                    if 'errors' in trade_result and trade_result['errors']:
                        print("\nERRORS:")
                        for i, error in enumerate(trade_result['errors'], 1):
                            asset = error.get('asset', 'UNKNOWN')
                            action = error.get('action', 'UNKNOWN')
                            reason = error.get('reason', 'Unknown reason')
                            print(f"  {i}. {action.upper()} {asset}: {reason}")

                    # Print rejected assets
                    if 'rejected_assets' in trade_result and trade_result['rejected_assets']:
                        print("\nREJECTED ASSETS:")
                        for asset, reason in trade_result['rejected_assets'].items():
                            print(f"  - {asset}: {reason}")

                    # Get available balance after trades
                    if self.trading_config.get('mode') == 'paper':
                        after_balance = self.trading_executor.paper_trading.get_balance().get(quote_currency, 0.0)
                    else:
                        after_balance = self.trading_executor.account_manager.get_balance(quote_currency)

                    print(f"\nAVAILABLE BALANCE AFTER TRADES: {after_balance:.8f} {quote_currency}")

                    # Log the trade result directly
                    try:
                        # Get the trading mode
                        trading_mode = self.trading_config.get('mode', 'paper')

                        # Log the entire trade result
                        trade_logger.log_trade_result(trade_result, trading_mode)
                        logging.info(f"Multi-asset trade result logged to trade log file")
                    except Exception as e:
                        logging.error(f"Error logging multi-asset trade result: {e}", exc_info=True)

                    # Check if any trades were successful, even if the overall result was a failure
                    successful_trades = [trade for trade in trade_result.get('trades', [])
                                        if trade.get('success', False) and trade.get('amount', 0) > 0 and trade.get('price', 0) > 0]

                    if successful_trades:
                        logging.info(f"Multi-asset trades partially executed: {len(successful_trades)} of {len(trade_result.get('trades', []))} trades successful")

                        # Send trade notification for each successful trade
                        if self.notification_manager:
                            for trade in successful_trades:
                                # Extract filled amount and fee from order
                                order = trade.get('order', {})
                                filled = order.get('filled', trade.get('amount', 0))
                                fee = 'Unknown'
                                if 'fee' in order and order['fee'] is not None:
                                    fee_cost = order['fee'].get('cost', 0)
                                    fee_currency = order['fee'].get('currency', '')
                                    fee = f"{fee_cost} {fee_currency}" if fee_cost > 0 else 'None'

                                self.notification_manager.notify(
                                    'trade_executed',
                                    {
                                        'action': trade.get('side', 'UNKNOWN'),
                                        'asset': trade.get('symbol', 'UNKNOWN'),
                                        'price': trade.get('price', 0),
                                        'amount': trade.get('amount', 0),
                                        'filled': filled,
                                        'fee': fee
                                    }
                                )

                        # Log any failed trades
                        failed_trades = [trade for trade in trade_result.get('trades', []) if not trade.get('success', False)]
                        if failed_trades:
                            logging.warning(f"Some trades failed: {len(failed_trades)} trades failed")
                            for failed in trade_result.get('errors', []):
                                logging.warning(f"Failed trade for {failed.get('asset')}: {failed.get('reason')}")
                    elif trade_result.get('success', False):
                        logging.info(f"Multi-asset trades executed: {len(trade_result.get('trades', []))} trades")

                        # Send trade notification for each trade
                        if self.notification_manager:
                            for trade in trade_result.get('trades', []):
                                if trade.get('success', False) and trade.get('amount', 0) > 0 and trade.get('price', 0) > 0:
                                    # Only send notification for actual trades with non-zero amounts and prices
                                    # Extract filled amount and fee from order
                                    order = trade.get('order', {})
                                    filled = order.get('filled', trade.get('amount', 0))
                                    fee = 'Unknown'
                                    if 'fee' in order and order['fee'] is not None:
                                        fee_cost = order['fee'].get('cost', 0)
                                        fee_currency = order['fee'].get('currency', '')
                                        fee = f"{fee_cost} {fee_currency}" if fee_cost > 0 else 'None'

                                    # Get the actual execution price from the order
                                    execution_price = trade.get('price', 0)
                                    if 'average_price' in order and order['average_price'] is not None:
                                        execution_price = order['average_price']
                                    elif 'average' in order and order['average'] is not None:
                                        execution_price = float(order['average'])
                                    elif 'price' in order and order['price'] is not None:
                                        execution_price = float(order['price'])

                                    self.notification_manager.notify(
                                        'trade_executed',
                                        {
                                            'action': trade.get('side', 'UNKNOWN'),
                                            'asset': trade.get('symbol', 'UNKNOWN'),
                                            'price': execution_price,
                                            'amount': trade.get('amount', 0),
                                            'filled': filled,
                                            'fee': fee,
                                            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
                                        }
                                    )
                    else:
                        logging.error(f"Multi-asset trade execution failed completely: {trade_result}")

                        # Send error notification
                        if self.notification_manager:
                            self.notification_manager.notify(
                                'error_alert',
                                {
                                    'error': trade_result.get('reason', 'Unknown error'),
                                    'component': 'trade_execution'
                                }
                            )
                elif best_asset:
                    # Use single-asset trading for n_assets = 1 or if only one asset is held
                    logging.info(f"Executing single-asset strategy with best asset: {best_asset}")
                    trade_result = self.trading_executor.execute_strategy_signal(
                        best_asset, mtpi_signal
                    )

                    # Log the trade result directly
                    try:
                        # Get the trading mode
                        trading_mode = self.trading_config.get('mode', 'paper')

                        # Log the entire trade result
                        trade_logger.log_trade_result(trade_result, trading_mode)
                        logging.info(f"Single-asset trade result logged to trade log file")
                    except Exception as e:
                        logging.error(f"Error logging single-asset trade result: {e}", exc_info=True)

                    if trade_result.get('success', False):
                        # Check if this was a skipped trade
                        reason = trade_result.get('reason', '')
                        if reason and ("below minimum order size" in reason.lower() or "does not exist on exchange" in reason.lower()):
                            logging.info(f"Trade skipped: {trade_result}")
                        else:
                            logging.info(f"Trade executed: {trade_result}")

                        # Send trade notification only if there's an actual trade (non-zero amount and price)
                        if self.notification_manager and trade_result.get('amount', 0) > 0 and trade_result.get('price', 0) > 0:
                            # Extract filled amount and fee from order
                            order = trade_result.get('order', {})
                            filled = order.get('filled', trade_result.get('amount', 0))
                            fee = 'Unknown'
                            if 'fee' in order and order['fee'] is not None:
                                fee_cost = order['fee'].get('cost', 0)
                                fee_currency = order['fee'].get('currency', '')
                                fee = f"{fee_cost} {fee_currency}" if fee_cost > 0 else 'None'

                            # Get the actual execution price from the order
                            execution_price = trade_result.get('price', 0)
                            if 'average_price' in order and order['average_price'] is not None:
                                execution_price = order['average_price']
                            elif 'average' in order and order['average'] is not None:
                                execution_price = float(order['average'])
                            elif 'price' in order and order['price'] is not None:
                                execution_price = float(order['price'])

                            self.notification_manager.notify(
                                'trade_executed',
                                {
                                    'action': trade_result.get('side', 'UNKNOWN'),
                                    'asset': trade_result.get('symbol', 'UNKNOWN'),
                                    'price': execution_price,
                                    'amount': trade_result.get('amount', 0),
                                    'filled': filled,
                                    'fee': fee,
                                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
                                }
                            )
                    else:
                        logging.error(f"Trade execution failed: {trade_result}")

                        # Send error notification with more detailed error information
                        if self.notification_manager:
                            error_details = trade_result.get('reason', 'Unknown error')
                            if 'errors' in trade_result and trade_result['errors']:
                                error_list = [f"{err.get('asset', 'Unknown')}: {err.get('reason', 'Unknown reason')}"
                                            for err in trade_result['errors']]
                                error_details = f"{error_details}. Details: {'; '.join(error_list)}"

                            self.notification_manager.notify(
                                'error_alert',
                                {
                                    'error': error_details,
                                    'component': 'trade_execution'
                                }
                            )
                elif use_mtpi_signal and mtpi_signal != 1:
                    # If MTPI is enabled and signal is bearish, and no assets are held, exit all positions
                    logging.info(f"MTPI signal is bearish ({mtpi_signal}). Exiting all positions.")
                    trade_result = self.trading_executor.exit_all_positions()

                    # Log the trade result directly
                    try:
                        # Get the trading mode
                        trading_mode = self.trading_config.get('mode', 'paper')

                        # Log the entire trade result
                        trade_logger.log_trade_result(trade_result, trading_mode)
                        logging.info(f"Exit all positions result logged to trade log file")
                    except Exception as e:
                        logging.error(f"Error logging exit all positions result: {e}", exc_info=True)

                    if trade_result.get('success', False):
                        logging.info(f"All positions exited successfully")

                        # Send trade notifications for each exited position
                        if self.notification_manager:
                            for trade in trade_result.get('results', []):
                                if trade.get('success', False):
                                    # Extract filled amount and fee from order
                                    order = trade.get('order', {})
                                    filled = order.get('filled', trade.get('amount', 0))
                                    fee = 'Unknown'
                                    if 'fee' in order and order['fee'] is not None:
                                        fee_cost = order['fee'].get('cost', 0)
                                        fee_currency = order['fee'].get('currency', '')
                                        fee = f"{fee_cost} {fee_currency}" if fee_cost > 0 else 'None'

                                    # Get the actual execution price from the order
                                    execution_price = trade.get('price', 0)
                                    if 'average_price' in order and order['average_price'] is not None:
                                        execution_price = order['average_price']
                                    elif 'average' in order and order['average'] is not None:
                                        execution_price = float(order['average'])
                                    elif 'price' in order and order['price'] is not None:
                                        execution_price = float(order['price'])

                                    self.notification_manager.notify(
                                        'trade_executed',
                                        {
                                            'action': 'sell',  # Exit positions are always sells
                                            'asset': trade.get('symbol', 'UNKNOWN'),
                                            'price': execution_price,
                                            'amount': trade.get('amount', 0),
                                            'filled': filled,
                                            'fee': fee,
                                            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
                                        }
                                    )
                    else:
                        logging.error(f"Failed to exit all positions: {trade_result}")

                        # Send error notification with more detailed error information
                        if self.notification_manager:
                            error_details = trade_result.get('reason', 'Failed to exit positions')
                            if 'results' in trade_result and trade_result['results']:
                                failed_exits = [f"{result.get('symbol', 'Unknown')}: {result.get('reason', 'Unknown reason')}"
                                              for result in trade_result['results'] if not result.get('success', False)]
                                if failed_exits:
                                    error_details = f"{error_details}. Failed exits: {'; '.join(failed_exits)}"

                            self.notification_manager.notify(
                                'error_alert',
                                {
                                    'error': error_details,
                                    'component': 'trade_execution'
                                }
                            )

            # Log asset selection details
            if 'performance_metrics' in results and 'latest_scores' in results['performance_metrics']:
                latest_scores = results['performance_metrics']['latest_scores']
                if latest_scores:
                    # Log asset selection with trade_logger
                    allocation_method = self._get_allocation_method_name()

                    # Identify any rejected assets (assets with high scores that weren't selected)
                    rejected_assets = {}
                    if len(latest_scores) > len(assets_held):
                        sorted_scores = sorted(latest_scores.items(), key=lambda x: x[1], reverse=True)
                        top_n = sorted_scores[:settings.get('n_assets', 1)]

                        for asset, score in top_n:
                            if asset not in assets_held:
                                rejected_assets[asset] = "Failed to trade"

                    # Log the asset selection
                    try:
                        # Convert any non-serializable objects to strings
                        serializable_scores = {k: float(v) for k, v in latest_scores.items()}
                        serializable_weights = {k: float(v) for k, v in assets_held.items()}

                        trade_logger.log_asset_selection(
                            timestamp=datetime.now().isoformat(),
                            all_assets_scores=serializable_scores,
                            selected_assets=list(assets_held.keys()),
                            mtpi_signal=int(mtpi_signal) if mtpi_signal is not None else 0,
                            allocation_method=allocation_method,
                            weights=serializable_weights,
                            rejected_assets=rejected_assets
                        )
                        logging.info(f"Asset selection logged with {len(serializable_scores)} assets scored and {len(assets_held)} assets selected")
                    except Exception as e:
                        logging.error(f"Error logging asset selection: {e}", exc_info=True)

            # Update state
            self.last_execution_time = datetime.now()
            self.last_mtpi_signal = mtpi_signal
            self.last_best_asset = best_asset

            # Try to extract MTPI score from results if available (only if MTPI is enabled)
            use_mtpi_signal = settings.get('use_mtpi_signal', True)
            if use_mtpi_signal:
                if 'mtpi_score' in results:
                    self.last_mtpi_score = results['mtpi_score']
                elif 'mtpi_signals' in results and results['mtpi_signals'] is not None:
                    # If we have MTPI signals but no score, try to calculate it
                    # This is a fallback for when the score isn't directly available
                    if isinstance(results['mtpi_signals'], pd.Series) and not results['mtpi_signals'].empty:
                        # For now, we'll use the signal value as a proxy
                        # In a future update, we could enhance this to get the actual score
                        self.last_mtpi_score = float(mtpi_signal) if mtpi_signal is not None else None
                    else:
                        self.last_mtpi_score = float(mtpi_signal) if mtpi_signal is not None else None
                else:
                    self.last_mtpi_score = float(mtpi_signal) if mtpi_signal is not None else None
            else:
                # When MTPI is disabled, don't store MTPI-related data
                self.last_mtpi_score = None
                logging.info("MTPI disabled - skipping MTPI score extraction")

            # No longer checking for snapshot path since we disabled snapshot saving
            snapshot_path = None

            # Send completion notification
            if self.notification_manager:
                # Get allocation approach
                allocation_approach = self.get_allocation_approach()

                # Check if position has changed
                position_changed = False
                if self.last_best_asset != best_asset:
                    position_changed = True
                    position_status = "Position changed"
                else:
                    position_status = "Position unchanged"

                # Check if MTPI signal has changed
                if self.last_mtpi_signal != mtpi_signal:
                    mtpi_status = "Signal changed"
                else:
                    mtpi_status = "Signal unchanged"

                # Format best asset display with change status
                if best_asset:
                    if position_changed:
                        best_asset_display = f"{best_asset} (new)"
                    else:
                        best_asset_display = f"{best_asset} (unchanged)"
                else:
                    best_asset_display = "None"

                # Get MTPI information for notification
                mtpi_info = self._get_mtpi_info_for_notification(mtpi_score=self.last_mtpi_score)

                notification_data = {
                    'status': 'completed',
                    'best_asset': best_asset_display,
                    'mtpi_info': mtpi_info,
                    'allocation_approach': allocation_approach,
                    'position_status': position_status,
                    'market_warning': ''  # Empty string for market warning
                }

                # Extract asset scores from results
                asset_scores = {}
                if 'performance_metrics' in results and 'latest_scores' in results['performance_metrics']:
                    asset_scores = results['performance_metrics']['latest_scores']
                    logging.info(f"Extracted asset scores: {asset_scores}")

                # Format asset scores for display
                asset_scores_str = ""
                if asset_scores:
                    # Sort assets by score in descending order
                    sorted_assets = sorted(asset_scores.items(), key=lambda x: x[1], reverse=True)

                    # Format the scores with ranking
                    score_lines = []
                    for i, (asset, score) in enumerate(sorted_assets):
                        # Add ranking position (1-based)
                        rank = i + 1
                        # Check if this asset is being held
                        is_held = asset in assets_held if assets_held else False
                        # Format with emoji indicator if held
                        indicator = "✅ " if is_held else ""
                        # Add the score line
                        score_lines.append(f"{indicator}{rank}. {asset}: {int(score)}")

                    # Join the score lines and add a header
                    asset_scores_str = "Asset scores:\n" + "\n".join(score_lines) + "\n\n"
                    notification_data['asset_scores_str'] = asset_scores_str
                else:
                    notification_data['asset_scores_str'] = ""

                # Add information about all assets being traded for multi-asset strategy
                if n_assets > 1 and assets_held and len(assets_held) > 0:
                    # Sort assets by their weights for display (highest weight first)
                    sorted_assets_by_weight = sorted(assets_held.items(), key=lambda x: x[1], reverse=True)

                    # Format assets and their weights for display
                    assets_str = ", ".join([f"{asset} ({weight:.1%})" for asset, weight in sorted_assets_by_weight])
                    notification_data['assets_traded'] = assets_str
                    notification_data['n_assets'] = len(assets_held)

                    # Check if there were any failed trades
                    failed_assets = []
                    if 'performance_metrics' in results and 'latest_scores' in results['performance_metrics']:
                        latest_scores = results['performance_metrics']['latest_scores']
                        if latest_scores:
                            # Get top N assets by score
                            sorted_by_score = sorted(latest_scores.items(), key=lambda x: x[1], reverse=True)[:n_assets]
                            top_assets = [asset for asset, _ in sorted_by_score]

                            # Log the top assets by score for debugging
                            logging.info(f"Top {n_assets} assets by score: {top_assets}")
                            print(f"TOP {n_assets} ASSETS BY SCORE:")
                            for i, (asset, score) in enumerate(sorted_by_score):
                                print(f"  {i+1}. {asset}: {score}")

                                # Check if this asset was rejected
                                if asset not in assets_held:
                                    print(f"  WARNING: HIGH-SCORING ASSET REJECTED: {asset} (rank {i+1}, score {score}) - Not in assets_held")
                                    logging.warning(f"HIGH-SCORING ASSET REJECTED: {asset} (rank {i+1}, score {score}) - Not in assets_held")

                            # Find assets that should have been traded but aren't in assets_held
                            failed_assets = [asset for asset in top_assets if asset not in assets_held]

                            # Log any failed assets
                            if failed_assets:
                                logging.warning(f"Failed to trade {len(failed_assets)} high-scoring assets: {failed_assets}")
                                for asset in failed_assets:
                                    score = next((s for a, s in sorted_by_score if a == asset), None)
                                    logging.warning(f"  HIGH-SCORING ASSET REJECTED: {asset} (score {score}) - Failed to trade")

                    # Create the assets traded string
                    if failed_assets:
                        notification_data['assets_traded_str'] = f"Assets traded (by weight) - {len(failed_assets)} failed:\n"
                    else:
                        notification_data['assets_traded_str'] = f"Assets traded (by weight):\n"

                    # Add each asset with its weight and score on a separate line
                    asset_lines = []
                    for asset, weight in sorted_assets_by_weight:
                        score = asset_scores.get(asset, 0) if asset_scores else 0
                        asset_lines.append(f"- {asset}: {weight:.1%} (score: {int(score)})")

                    # Add failed assets with a note
                    for asset in failed_assets:
                        score = asset_scores.get(asset, 0) if asset_scores else 0
                        asset_lines.append(f"- ❌ {asset}: 0% (score: {int(score)}) - trade failed")

                    notification_data['assets_traded_str'] += "\n".join(asset_lines) + "\n\n"

                    # For multi-asset strategies, modify the best_asset display to show it's the highest scoring
                    if best_asset in assets_held:
                        notification_data['best_asset'] = f"{best_asset_display} (highest score)"
                else:
                    # Empty string for single asset strategies
                    notification_data['assets_traded_str'] = ""

                # No longer adding snapshot information to notifications

                # Determine which template to use based on MTPI signal (only if MTPI is enabled)
                use_mtpi_signal = settings.get('use_mtpi_signal', True)
                template_name = 'strategy_execution_bearish' if (use_mtpi_signal and mtpi_signal == -1) else 'strategy_execution'

                self.notification_manager.notify(
                    template_name,
                    notification_data
                )

            execution_end_time = datetime.now()
            execution_duration = (execution_end_time - execution_start_time).total_seconds()

            logging.info(f"Strategy execution completed successfully in {execution_duration:.2f} seconds")
            print("\n" + "=" * 50)
            print(f"STRATEGY EXECUTION COMPLETED SUCCESSFULLY")
            print(f"Duration: {execution_duration:.2f} seconds")

            # Record successful execution
            self.recovery_manager.record_successful_execution(
                'strategy_execution',
                {
                    'timeframe': self.config.get('settings', {}).get('timeframe', '1d'),
                    'execution_time': execution_start_time.isoformat()
                }
            )

            # Display next execution time based on timeframe
            if timeframe == '1d':
                print(f"Next execution will be at 00:01 UTC tomorrow")
            elif timeframe == '4h':
                current_hour = datetime.now().hour
                next_4h_mark = ((current_hour // 4) + 1) * 4
                if next_4h_mark >= 24:
                    next_4h_mark = 0
                print(f"Next execution will be at {next_4h_mark:02d}:01 UTC")
            elif timeframe == '1h':
                print(f"Next execution will be at the next hour :01 minute")
            elif timeframe == '1m':
                print(f"Next execution will be in approximately 1 minute")
            else:
                print(f"Next execution will be according to schedule ({timeframe} timeframe)")

            print("=" * 50)

        except Exception as e:
            logging.error(f"Error executing strategy: {e}", exc_info=True)
            print(f"\nERROR EXECUTING STRATEGY: {e}")
            print("Check the log file for more details")

            # Record the strategy execution failure
            self.recovery_manager.record_failed_execution(
                'strategy_execution',
                str(e),
                {
                    'timeframe': self.config.get('settings', {}).get('timeframe', '1d'),
                    'is_recovery': is_recovery
                }
            )

            # Try to recover from the strategy execution failure
            if not is_recovery:  # Avoid infinite recursion
                self.recovery_manager.recover_from_failure(
                    'strategy_execution_failure',
                    {
                        'error': str(e),
                        'is_recovery': is_recovery
                    }
                )

            # Send error notification
            if self.notification_manager:
                print("Sending error notification...")
                self.notification_manager.notify(
                    'error_alert',
                    {
                        'error': str(e),
                        'component': 'strategy_execution'
                    }
                )

    def cleanup_cache_files(self):
        """Clean up cache files by removing duplicates and ensuring they're properly sorted."""
        try:
            logging.info("Starting scheduled cache file cleanup")
            print("\n" + "=" * 50)
            print("STARTING SCHEDULED CACHE FILE CLEANUP")
            print("=" * 50)

            # Get settings
            settings = self.config.get('settings', {})
            exchange_id = self.config.get('exchange', 'binance')
            timeframe = settings.get('timeframe', '1d')
            # Use exchange-agnostic defaults
            from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
            exchange_id = self.config.get('exchange', 'binance')
            trading_quote = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')
            assets = settings.get('assets', [f'BTC/{trading_quote}', f'ETH/{trading_quote}', f'SOL/{trading_quote}'])

            print(f"Cleaning up cache files for {len(assets)} assets")
            print(f"Exchange: {exchange_id}")
            print(f"Timeframe: {timeframe}")

            # Clean up each asset's cache file
            success_count = 0
            failure_count = 0

            for symbol in assets:
                print(f"Cleaning up cache file for {symbol}...")

                success = cleanup_cache_file(
                    exchange_id=exchange_id,
                    symbol=symbol,
                    timeframe=timeframe
                )

                if success:
                    success_count += 1
                    print(f"  ✓ Successfully cleaned up cache file for {symbol}")
                    logging.info(f"Successfully cleaned up cache file for {symbol}")
                else:
                    failure_count += 1
                    print(f"  ✗ Failed to clean up cache file for {symbol}")
                    logging.warning(f"Failed to clean up cache file for {symbol}")

            print("\nCache cleanup completed:")
            print(f"  - Successfully cleaned: {success_count} files")
            print(f"  - Failed to clean: {failure_count} files")

            logging.info(f"Completed scheduled cache file cleanup: {success_count} successful, {failure_count} failed")

            # Send notification if available
            if self.notification_manager:
                self.notification_manager.notify(
                    'maintenance',
                    {
                        'action': 'cache_cleanup',
                        'success_count': success_count,
                        'failure_count': failure_count,
                        'total_assets': len(assets)
                    }
                )

        except Exception as e:
            logging.error(f"Error during cache file cleanup: {e}")
            print(f"ERROR during cache file cleanup: {e}")

            # Send error notification
            if self.notification_manager:
                self.notification_manager.notify(
                    'error_alert',
                    {
                        'error': str(e),
                        'component': 'cache_cleanup'
                    }
                )

    def generate_performance_report(self):
        """Generate a performance report from the CSV metrics data."""
        try:
            logging.info("Generating performance report from CSV metrics data")
            print("\n" + "=" * 50)
            print("GENERATING PERFORMANCE REPORT")
            print("=" * 50)

            # Get settings from config
            settings = self.config.get('settings', {})
            timeframe = settings.get('timeframe', '1d')
            mtpi_timeframe = settings.get('mtpi_timeframe', '1d')
            n_assets = settings.get('n_assets', 1)
            use_weighted_allocation = settings.get('use_weighted_allocation', False)
            weights = settings.get('weights', None)

            # Determine strategy name
            strategy_name = "AssetRotation"
            if n_assets == 1:
                strategy_name = "BestAsset"
            elif use_weighted_allocation:
                strategy_name = "WeightedAllocation"
            else:
                strategy_name = f"EqualAllocation_{n_assets}"

            # Get the number of assets for the filename
            # Use exchange-agnostic defaults
            from src.trading.executor import EXCHANGE_QUOTE_CURRENCIES
            exchange_id = self.config.get('exchange', 'binance')
            trading_quote = EXCHANGE_QUOTE_CURRENCIES.get(exchange_id.lower(), 'USDC')
            assets = settings.get('assets', [f'BTC/{trading_quote}', f'ETH/{trading_quote}', f'SOL/{trading_quote}'])
            asset_count = len(assets)
            logging.info(f"Using asset count {asset_count} for metrics filename")
            print(f"ASSET COUNT: {asset_count}")

            # Load metrics history
            metrics_df = load_metrics_history(
                strategy_name=strategy_name,
                timeframe=timeframe,
                mtpi_timeframe=mtpi_timeframe,
                metrics_dir=self.metrics_dir,
                weighted=use_weighted_allocation,
                weights=weights,
                run_id=self.run_id,  # Pass the run ID to load the correct file
                asset_count=asset_count  # Pass the asset count for the filename
            )

            if metrics_df.empty:
                logging.warning("No metrics data found")
                print("No metrics data found")
                return False

            # Print basic statistics
            print(f"\nLoaded {len(metrics_df)} metrics records")
            print(f"Date range: {metrics_df['timestamp'].min()} to {metrics_df['timestamp'].max()}")

            # Generate plots for key metrics
            key_metrics = [
                'strategy_total_increase',
                'strategy_sharpe_ratio',
                'strategy_sortino_ratio',
                'strategy_max_drawdown'
            ]

            plot_files = []
            for metric in key_metrics:
                if metric in metrics_df.columns:
                    try:
                        plot_file = plot_metrics_history(
                            metrics_df=metrics_df,
                            metric_name=metric,
                            output_dir=self.metrics_dir,
                            save_plot=True,
                            show_plot=False
                        )
                        if plot_file:
                            plot_files.append(plot_file)
                            print(f"Generated plot for {metric}: {plot_file}")
                    except Exception as e:
                        logging.error(f"Error generating plot for {metric}: {e}")

            # Send notification with report summary
            if self.notification_manager:
                # Prepare message with latest metrics
                if not metrics_df.empty:
                    latest_metrics = metrics_df.iloc[-1]

                    # Format key metrics for display
                    total_increase = latest_metrics.get('strategy_total_increase', 'N/A')
                    if isinstance(total_increase, (int, float)):
                        total_increase = f"{total_increase:.2f}%"

                    sharpe_ratio = latest_metrics.get('strategy_sharpe_ratio', 'N/A')
                    if isinstance(sharpe_ratio, (int, float)):
                        sharpe_ratio = f"{sharpe_ratio:.2f}"

                    max_drawdown = latest_metrics.get('strategy_max_drawdown', 'N/A')
                    if isinstance(max_drawdown, (int, float)):
                        max_drawdown = f"{max_drawdown:.2f}%"

                    # Create message
                    message = (
                        f"Performance Report ({datetime.now().strftime('%Y-%m-%d %H:%M')})\n\n"
                        f"Strategy: {strategy_name}\n"
                        f"Timeframe: {timeframe}\n"
                        f"MTPI Timeframe: {mtpi_timeframe}\n\n"
                        f"Latest Metrics:\n"
                        f"- Total Return: {total_increase}\n"
                        f"- Sharpe Ratio: {sharpe_ratio}\n"
                        f"- Max Drawdown: {max_drawdown}\n\n"
                        f"Data points: {len(metrics_df)}\n"
                        f"Generated plots: {len(plot_files)}"
                    )

                    self.notification_manager.notify(
                        'performance_report',
                        {
                            'message': message,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'metrics_count': len(metrics_df),
                            'plots_generated': len(plot_files)
                        }
                    )

            return True
        except Exception as e:
            logging.error(f"Error generating performance report: {e}", exc_info=True)
            return False

    def send_status_update(self):
        """Send a status update notification."""
        try:
            if not self.notification_manager:
                return

            # Get account information if trading is enabled
            account_info = {}
            if self.trading_config.get('enabled', False):
                balance = self.account_manager.get_balance()
                positions = self.account_manager.get_open_positions()

                account_info = {
                    'balance': balance,
                    'positions': positions
                }

            # Get current portfolio information
            portfolio_info = {}
            if self.trading_config.get('enabled', False):
                trading_status = self.trading_executor.get_trading_status()
                portfolio_info = {
                    'current_asset': trading_status.get('current_asset', None),
                    'portfolio': trading_status.get('current_portfolio', {}),
                    'actual_weights': trading_status.get('actual_weights', {}),
                    'portfolio_value': trading_status.get('portfolio_value', 0)
                }

            # Send status notification
            self.notification_manager.notify(
                'status_update',
                {
                    'running': self.is_running,
                    'last_execution': self.last_execution_time.strftime('%Y-%m-%d %H:%M:%S UTC') if self.last_execution_time else 'Never',
                    'mtpi_signal': 'bullish' if self.last_mtpi_signal == 1 else 'bearish' if self.last_mtpi_signal == -1 else 'Unknown',
                    'best_asset': self.last_best_asset or 'None',
                    'account': account_info,
                    'portfolio': portfolio_info
                }
            )

            logging.info("Status update sent")

        except Exception as e:
            logging.error(f"Error sending status update: {e}")



def main():
    """Main entry point for the background service."""
    # Print welcome banner
    print("\n" + "=" * 70)
    print("ASSET ROTATION STRATEGY BACKGROUND SERVICE")
    print("=" * 70)
    print("This service runs the asset rotation strategy continuously")
    print("Press Ctrl+C to stop the service")
    print("=" * 70 + "\n")

    parser = argparse.ArgumentParser(description='Asset Rotation Strategy Background Service')
    parser.add_argument('--config', help='Path to configuration file')
    parser.add_argument('--notifications', help='Path to notification configuration file')
    parser.add_argument('--log-level', default='INFO', help='Logging level')
    parser.add_argument('--no-incremental', action='store_true', help='Disable incremental data fetching')
    parser.add_argument('--test', action='store_true', help='Use test Telegram bot instead of live bot')
    args = parser.parse_args()

    # Configure logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('background_service.log')
        ]
    )

    print(f"Log level set to: {args.log_level}")
    print(f"Logs will be saved to: background_service.log")
    if args.config:
        print(f"Using config file: {args.config}")
    else:
        print("Using default config file: config/settings.yaml")
    if args.test:
        print("*** RUNNING IN TEST MODE - Using test Telegram bot ***")

    # Create and start the service
    try:
        # Load configuration
        config = load_config(args.config)

        # Update configuration based on command-line arguments
        if args.no_incremental:
            print("Incremental data fetching disabled via command-line argument")
            if 'settings' not in config:
                config['settings'] = {}
            config['settings']['force_refresh_cache'] = True

            # Save the updated configuration
            from src.config_manager import save_config
            save_config(config)

        # Create and start the service
        service = BackgroundService(args.config, notification_config_path=args.notifications, test_mode=args.test)
        service.start()

        # Keep the main thread alive
        print("\nService is running. Press Ctrl+C to stop.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nKeyboard interrupt received, shutting down...")
        logging.info("Keyboard interrupt received, shutting down...")
        if 'service' in locals():
            service.stop()
    except Exception as e:
        print(f"\nError in main thread: {e}")
        logging.error(f"Error in main thread: {e}", exc_info=True)
        if 'service' in locals():
            service.stop()

if __name__ == "__main__":
    main()
