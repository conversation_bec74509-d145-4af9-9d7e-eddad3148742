2025-06-26 17:32:15,667 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:32:16,552 - root - INFO - Loaded 47 trade records from logs/trades\trade_log_20250626.json
2025-06-26 17:32:16,553 - root - INFO - Loaded 28 asset selection records from logs/trades\asset_selection_20250626.json
2025-06-26 17:32:16,554 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:32:17,736 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:17,744 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:17,745 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:17,753 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:17,753 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:17,759 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:17,761 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:32:17,762 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:32:18,766 - root - INFO - Telegram command handlers registered
2025-06-26 17:32:18,766 - root - INFO - Telegram bot polling started
2025-06-26 17:32:18,766 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:32:18,766 - root - INFO - Telegram notification channel initialized
2025-06-26 17:32:18,768 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:32:18,768 - root - INFO - Loaded 24 templates from file
2025-06-26 17:32:18,769 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:32:18,769 - root - INFO - Notification manager initialized
2025-06-26 17:32:18,769 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:32:18,770 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:32:18,770 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:32:18,770 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:32:18,776 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:32:18,776 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_20250626_114325.json.json
2025-06-26 17:32:18,777 - root - INFO - Recovery manager initialized
2025-06-26 17:32:18,777 - root - ERROR - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-26 17:32:18,777 - root - ERROR - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-26 17:32:18,777 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:18,786 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:18,786 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:32:18,786 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:32:18,787 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:32:18,787 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:32:18,787 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:32:18,787 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:32:18,788 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:32:18,788 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:32:18,788 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:18,797 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:18,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:32:18,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:32:18,842 - telegram.ext.Application - INFO - Application started
